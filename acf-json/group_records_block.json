{"key": "group_records_block", "title": "Records Block", "fields": [{"key": "field_records_block_title", "label": "Title", "name": "title", "type": "text", "instructions": "Enter a title for the records section", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "Latest Records", "placeholder": "", "prepend": "", "append": "", "maxlength": ""}, {"key": "field_records_block_number", "label": "Number of Records", "name": "number", "type": "number", "instructions": "How many records to display", "required": 0, "conditional_logic": 0, "wrapper": {"width": "33", "class": "", "id": ""}, "default_value": 6, "placeholder": "", "prepend": "", "append": "", "min": 1, "max": 24, "step": 1}, {"key": "field_records_block_columns", "label": "Columns", "name": "columns", "type": "number", "instructions": "Number of columns to display", "required": 0, "conditional_logic": 0, "wrapper": {"width": "33", "class": "", "id": ""}, "default_value": 3, "placeholder": "", "prepend": "", "append": "", "min": 1, "max": 6, "step": 1}, {"key": "field_records_block_show_filters", "label": "Show Filters", "name": "show_filters", "type": "true_false", "instructions": "Show genre and artist filters", "required": 0, "conditional_logic": 0, "wrapper": {"width": "33", "class": "", "id": ""}, "message": "Show filters", "default_value": 0, "ui": 1, "ui_on_text": "Yes", "ui_off_text": "No"}, {"key": "field_records_block_genre", "label": "Genre", "name": "genre", "type": "taxonomy", "instructions": "Filter by genre (optional)", "required": 0, "conditional_logic": 0, "wrapper": {"width": "33", "class": "", "id": ""}, "taxonomy": "record_genre", "field_type": "select", "allow_null": 1, "add_term": 0, "save_terms": 0, "load_terms": 0, "return_format": "id", "multiple": 0}, {"key": "field_records_block_artist", "label": "Artist", "name": "artist", "type": "taxonomy", "instructions": "Filter by artist (optional)", "required": 0, "conditional_logic": 0, "wrapper": {"width": "33", "class": "", "id": ""}, "taxonomy": "record_artist", "field_type": "select", "allow_null": 1, "add_term": 0, "save_terms": 0, "load_terms": 0, "return_format": "id", "multiple": 0}, {"key": "field_records_block_format", "label": "Format", "name": "format", "type": "taxonomy", "instructions": "Filter by format (optional)", "required": 0, "conditional_logic": 0, "wrapper": {"width": "33", "class": "", "id": ""}, "taxonomy": "record_format", "field_type": "select", "allow_null": 1, "add_term": 0, "save_terms": 0, "load_terms": 0, "return_format": "id", "multiple": 0}, {"key": "field_records_block_view_all_link", "label": "View All Link", "name": "view_all_link", "type": "url", "instructions": "URL for the 'View All' button (leave empty to hide button)", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": ""}], "location": [[{"param": "block", "operator": "==", "value": "acf/records-block"}]], "menu_order": 0, "position": "normal", "style": "default", "label_placement": "top", "instruction_placement": "label", "hide_on_screen": "", "active": true, "description": "", "show_in_rest": 0, "modified": 1684500000}