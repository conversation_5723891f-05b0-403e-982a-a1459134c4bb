{"key": "group_images_text_block", "title": "Images Text Block", "fields": [{"key": "field_images_text_title", "label": "Title", "name": "title", "type": "text", "instructions": "Enter the title for this section", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": ""}, {"key": "field_images_text_content", "label": "Content", "name": "content", "type": "wysiwyg", "instructions": "Enter the text content", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "tabs": "all", "toolbar": "full", "media_upload": 1, "delay": 0}, {"key": "field_images_text_popup_image", "label": "Popup Image", "name": "popup_image", "type": "image", "instructions": "Upload an image that will appear above the content", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "return_format": "array", "preview_size": "medium", "library": "all", "min_width": "", "min_height": "", "min_size": "", "max_width": "", "max_height": "", "max_size": "", "mime_types": ""}, {"key": "field_images_text_middle_image", "label": "Middle Column Image", "name": "middle_image", "type": "image", "instructions": "Upload an image that will appear below the content in the middle column", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "return_format": "array", "preview_size": "medium", "library": "all", "min_width": "", "min_height": "", "min_size": "", "max_width": "", "max_height": "", "max_size": "", "mime_types": ""}, {"key": "field_images_text_links", "label": "Links", "name": "links", "type": "repeater", "instructions": "Add links to display in this section", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "collapsed": "field_images_text_link_text", "min": 0, "max": 5, "layout": "table", "button_label": "Add Link", "sub_fields": [{"key": "field_images_text_link_text", "label": "Link Text", "name": "link_text", "type": "text", "instructions": "", "required": 1, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": ""}, {"key": "field_images_text_link_url", "label": "Link URL", "name": "link_url", "type": "url", "instructions": "", "required": 1, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": ""}, {"key": "field_images_text_link_target", "label": "Open in New Tab", "name": "link_target", "type": "true_false", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "message": "Yes", "default_value": 0, "ui": 1, "ui_on_text": "Yes", "ui_off_text": "No"}]}, {"key": "field_images_text_col1_media_type", "label": "Column 1 Media Type", "name": "col1_media_type", "type": "radio", "instructions": "Select the type of media to display in the first column", "required": 1, "conditional_logic": 0, "wrapper": {"width": "50", "class": "", "id": ""}, "choices": {"image": "Image", "video": "Video"}, "allow_null": 0, "other_choice": 0, "default_value": "image", "layout": "horizontal", "return_format": "value", "save_other_choice": 0}, {"key": "field_images_text_col1_image", "label": "Column 1 Image", "name": "col1_image", "type": "image", "instructions": "Upload an image for the first column", "required": 0, "conditional_logic": [[{"field": "field_images_text_col1_media_type", "operator": "==", "value": "image"}]], "wrapper": {"width": "50", "class": "", "id": ""}, "return_format": "array", "preview_size": "medium", "library": "all", "min_width": "", "min_height": "", "min_size": "", "max_width": "", "max_height": "", "max_size": "", "mime_types": ""}, {"key": "field_images_text_col1_video_url", "label": "Column 1 Video URL", "name": "col1_video_url", "type": "url", "instructions": "Enter the URL for the video in the first column (MP4 format)", "required": 0, "conditional_logic": [[{"field": "field_images_text_col1_media_type", "operator": "==", "value": "video"}]], "wrapper": {"width": "50", "class": "", "id": ""}, "default_value": "", "placeholder": "https://example.com/video.mp4"}, {"key": "field_images_text_col3_media_type", "label": "Column 3 Media Type", "name": "col3_media_type", "type": "radio", "instructions": "Select the type of media to display in the third column", "required": 1, "conditional_logic": 0, "wrapper": {"width": "50", "class": "", "id": ""}, "choices": {"image": "Image", "video": "Video"}, "allow_null": 0, "other_choice": 0, "default_value": "image", "layout": "horizontal", "return_format": "value", "save_other_choice": 0}, {"key": "field_images_text_col3_image", "label": "Column 3 Image", "name": "col3_image", "type": "image", "instructions": "Upload an image for the third column", "required": 0, "conditional_logic": [[{"field": "field_images_text_col3_media_type", "operator": "==", "value": "image"}]], "wrapper": {"width": "50", "class": "", "id": ""}, "return_format": "array", "preview_size": "medium", "library": "all", "min_width": "", "min_height": "", "min_size": "", "max_width": "", "max_height": "", "max_size": "", "mime_types": ""}, {"key": "field_images_text_col3_video_url", "label": "Column 3 Video URL", "name": "col3_video_url", "type": "url", "instructions": "Enter the URL for the video in the third column (MP4 format)", "required": 0, "conditional_logic": [[{"field": "field_images_text_col3_media_type", "operator": "==", "value": "video"}]], "wrapper": {"width": "50", "class": "", "id": ""}, "default_value": "", "placeholder": "https://example.com/video.mp4"}, {"key": "field_images_text_anchor", "label": "<PERSON><PERSON>", "name": "anchor", "type": "text", "instructions": "Enter an optional anchor ID for this block (for navigation links)", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "section-name", "prepend": "#", "append": "", "maxlength": ""}], "location": [[{"param": "block", "operator": "==", "value": "acf/images-text-block"}]], "menu_order": 0, "position": "normal", "style": "default", "label_placement": "top", "instruction_placement": "label", "hide_on_screen": "", "active": true, "description": "", "show_in_rest": 0}