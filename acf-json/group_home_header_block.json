{"key": "group_home_header_block", "title": "Home Header Block", "fields": [{"key": "field_home_header_title", "label": "Title", "name": "title", "aria-label": "", "type": "text", "instructions": "Enter the main title for the header", "required": 1, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "Rewindrecords", "placeholder": "", "prepend": "", "append": "", "maxlength": ""}, {"key": "field_home_header_text", "label": "Text", "name": "text", "aria-label": "", "type": "textarea", "instructions": "Enter the description text", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "maxlength": "", "rows": "", "placeholder": "", "new_lines": "br"}, {"key": "field_home_header_media_type", "label": "Media Type", "name": "media_type", "aria-label": "", "type": "radio", "instructions": "Select the type of media to display", "required": 1, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "choices": {"image": "Image", "video": "Video"}, "allow_null": 0, "other_choice": 0, "default_value": "image", "layout": "horizontal", "return_format": "value", "save_other_choice": 0}, {"key": "field_home_header_image", "label": "Image", "name": "image", "aria-label": "", "type": "image", "instructions": "Upload an image for the header", "required": 0, "conditional_logic": [[{"field": "field_home_header_media_type", "operator": "==", "value": "image"}]], "wrapper": {"width": "", "class": "", "id": ""}, "return_format": "array", "preview_size": "medium", "library": "all", "min_width": "", "min_height": "", "min_size": "", "max_width": "", "max_height": "", "max_size": "", "mime_types": ""}, {"key": "field_home_header_video_url", "label": "Video URL", "name": "video_url", "aria-label": "", "type": "url", "instructions": "Enter the URL for the video (MP4 format)", "required": 0, "conditional_logic": [[{"field": "field_home_header_media_type", "operator": "==", "value": "video"}]], "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "https://example.com/video.mp4"}, {"key": "field_home_header_show_location", "label": "Show Location", "name": "show_location", "aria-label": "", "type": "true_false", "instructions": "Show the store location from site settings", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "message": "Yes, show location", "default_value": 1, "ui": 1, "ui_on_text": "Yes", "ui_off_text": "No"}, {"key": "field_home_header_links", "label": "Links", "name": "links", "aria-label": "", "type": "repeater", "instructions": "Add links to display in the header", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "collapsed": "field_home_header_link_text", "min": 0, "max": 5, "layout": "table", "button_label": "Add Link", "rows_per_page": 20, "sub_fields": [{"key": "field_home_header_link_text", "label": "Link Text", "name": "link_text", "aria-label": "", "type": "text", "instructions": "", "required": 1, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": "", "parent_repeater": "field_home_header_links"}, {"key": "field_home_header_link_url", "label": "Link URL", "name": "link_url", "aria-label": "", "type": "url", "instructions": "", "required": 1, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "parent_repeater": "field_home_header_links"}, {"key": "field_home_header_link_target", "label": "Open in New Tab", "name": "link_target", "aria-label": "", "type": "true_false", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "message": "Yes", "default_value": 0, "ui": 1, "ui_on_text": "Yes", "ui_off_text": "No", "parent_repeater": "field_home_header_links"}]}, {"key": "field_home_header_anchor", "label": "<PERSON><PERSON>", "name": "anchor", "aria-label": "", "type": "text", "instructions": "Enter an optional anchor ID for this block (for navigation links)", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "home", "prepend": "#", "append": "", "maxlength": ""}], "location": [[{"param": "block", "operator": "==", "value": "acf/home-header-block"}]], "menu_order": 0, "position": "normal", "style": "default", "label_placement": "top", "instruction_placement": "label", "hide_on_screen": "", "active": true, "description": "", "show_in_rest": 0, "modified": 1747852358}