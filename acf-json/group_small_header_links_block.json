{"key": "group_small_header_links_block", "title": "Small Header Links Block", "fields": [{"key": "field_small_header_links_title", "label": "Title", "name": "title", "type": "text", "instructions": "Enter the main title for the header", "required": 1, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": ""}, {"key": "field_small_header_links_links", "label": "Links", "name": "links", "type": "repeater", "instructions": "Add links to display in this section", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "collapsed": "field_small_header_links_link_text", "min": 0, "max": 10, "layout": "table", "button_label": "Add Link", "sub_fields": [{"key": "field_small_header_links_link_text", "label": "Link Text", "name": "link_text", "type": "text", "instructions": "", "required": 1, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": ""}, {"key": "field_small_header_links_link_url", "label": "Link URL", "name": "link_url", "type": "url", "instructions": "", "required": 1, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": ""}, {"key": "field_small_header_links_link_target", "label": "Open in New Tab", "name": "link_target", "type": "true_false", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "message": "Yes", "default_value": 0, "ui": 1, "ui_on_text": "Yes", "ui_off_text": "No"}]}, {"key": "field_small_header_links_anchor", "label": "<PERSON><PERSON>", "name": "anchor", "type": "text", "instructions": "Enter an optional anchor ID for this block (for navigation links)", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "section-name", "prepend": "#", "append": "", "maxlength": ""}], "location": [[{"param": "block", "operator": "==", "value": "acf/small-header-links-block"}]], "menu_order": 0, "position": "normal", "style": "default", "label_placement": "top", "instruction_placement": "label", "hide_on_screen": "", "active": true, "description": "", "show_in_rest": 0}