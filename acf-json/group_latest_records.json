{"key": "group_latest_records", "title": "Latest Records", "fields": [{"key": "field_latest_records_title", "label": "Title", "name": "title", "aria-label": "", "type": "text", "instructions": "Enter a title for the section (optional)", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "What's New", "placeholder": "", "prepend": "", "append": "", "maxlength": ""}, {"key": "field_latest_records_count", "label": "Number of Records", "name": "records_count", "aria-label": "", "type": "number", "instructions": "How many latest records to display", "required": 1, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": 6, "placeholder": "", "prepend": "", "append": "", "min": 1, "max": 12, "step": 1}, {"key": "field_latest_records_columns", "label": "Columns", "name": "columns", "aria-label": "", "type": "select", "instructions": "Number of columns to display", "required": 1, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "choices": {"2": "2 Columns", "3": "3 Columns", "4": "4 Columns"}, "default_value": 3, "allow_null": 0, "multiple": 0, "ui": 1, "return_format": "value", "ajax": 0, "placeholder": ""}, {"key": "field_latest_records_show_artist", "label": "Show Artist", "name": "show_artist", "aria-label": "", "type": "true_false", "instructions": "Show the artist name", "required": 0, "conditional_logic": 0, "wrapper": {"width": "33", "class": "", "id": ""}, "message": "", "default_value": 1, "ui": 1, "ui_on_text": "Yes", "ui_off_text": "No"}, {"key": "field_latest_records_show_year", "label": "Show Year", "name": "show_year", "aria-label": "", "type": "true_false", "instructions": "Show the release year", "required": 0, "conditional_logic": 0, "wrapper": {"width": "33", "class": "", "id": ""}, "message": "", "default_value": 1, "ui": 1, "ui_on_text": "Yes", "ui_off_text": "No"}, {"key": "field_latest_records_show_format", "label": "Show Format", "name": "show_format", "aria-label": "", "type": "true_false", "instructions": "Show the record format", "required": 0, "conditional_logic": 0, "wrapper": {"width": "33", "class": "", "id": ""}, "message": "", "default_value": 1, "ui": 1, "ui_on_text": "Yes", "ui_off_text": "No"}, {"key": "field_682738146ca5e", "label": "Link", "name": "link", "aria-label": "", "type": "link", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "return_format": "array"}], "location": [[{"param": "block", "operator": "==", "value": "acf/latest-records"}]], "menu_order": 0, "position": "normal", "style": "default", "label_placement": "top", "instruction_placement": "label", "hide_on_screen": "", "active": true, "description": "", "show_in_rest": 0, "modified": 1747400794}