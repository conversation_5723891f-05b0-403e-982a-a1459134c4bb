{"key": "group_global_info_block", "title": "Global Info Block", "fields": [{"key": "field_global_info_title", "label": "Title", "name": "title", "aria-label": "", "type": "text", "instructions": "Enter the main title for the block (default: 'Kom bij ons langs')", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "Kom bij ons langs", "placeholder": "", "prepend": "", "append": "", "maxlength": ""}, {"key": "field_global_info_show_hours", "label": "Show Store Hours", "name": "show_hours", "aria-label": "", "type": "true_false", "instructions": "Display store opening hours", "required": 0, "conditional_logic": 0, "wrapper": {"width": "50", "class": "", "id": ""}, "message": "", "default_value": 1, "ui": 1, "ui_on_text": "Yes", "ui_off_text": "No"}, {"key": "field_global_info_custom_hours", "label": "Custom Hours Text", "name": "custom_hours", "aria-label": "", "type": "textarea", "instructions": "Enter custom opening hours text (leave empty to use default)", "required": 0, "conditional_logic": [[{"field": "field_global_info_show_hours", "operator": "==", "value": "1"}]], "wrapper": {"width": "50", "class": "", "id": ""}, "default_value": "", "placeholder": "<PERSON><PERSON><PERSON> tot vrijdag open van 11:00 tot 20:00.\r\nZondag open van 10:00 tot 17:00", "maxlength": "", "rows": 4, "new_lines": "br"}, {"key": "field_global_info_show_location", "label": "Show Location Info", "name": "show_location_info", "aria-label": "", "type": "true_false", "instructions": "Display store location information", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "message": "", "default_value": 1, "ui": 1, "ui_on_text": "Yes", "ui_off_text": "No"}, {"key": "field_global_info_media_type", "label": "Media Type", "name": "media_type", "aria-label": "", "type": "radio", "instructions": "Choose whether to display an image or a map", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "choices": {"image": "Image", "map": "Map (<PERSON> with <PERSON><PERSON>)"}, "allow_null": 0, "other_choice": 0, "default_value": "map", "layout": "horizontal", "return_format": "value", "save_other_choice": 0}, {"key": "field_global_info_image", "label": "Image", "name": "image", "aria-label": "", "type": "image", "instructions": "Upload an image to display in the left column", "required": 0, "conditional_logic": [[{"field": "field_global_info_media_type", "operator": "==", "value": "image"}]], "wrapper": {"width": "", "class": "", "id": ""}, "return_format": "array", "preview_size": "medium", "library": "all", "min_width": "", "min_height": "", "min_size": "", "max_width": "", "max_height": "", "max_size": "", "mime_types": ""}, {"key": "field_global_info_map_marker", "label": "Map Marker Image", "name": "map_marker_image", "aria-label": "", "type": "image", "instructions": "Upload an image to display in the map marker popup", "required": 0, "conditional_logic": [[{"field": "field_global_info_media_type", "operator": "==", "value": "map"}]], "wrapper": {"width": "", "class": "", "id": ""}, "return_format": "array", "preview_size": "medium", "library": "all", "min_width": "", "min_height": "", "min_size": "", "max_width": "", "max_height": "", "max_size": "", "mime_types": ""}, {"key": "field_global_info_links", "label": "Custom Links", "name": "links", "aria-label": "", "type": "repeater", "instructions": "Add custom links (leave empty to use default 'Routebeschrijving' and 'Contact' links)", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "collapsed": "field_global_info_link_text", "min": 0, "max": 5, "layout": "table", "button_label": "Add Link", "rows_per_page": 20, "sub_fields": [{"key": "field_global_info_link_text", "label": "Link Text", "name": "link_text", "aria-label": "", "type": "text", "instructions": "", "required": 1, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": "", "parent_repeater": "field_global_info_links"}, {"key": "field_global_info_link_url", "label": "Link URL", "name": "link_url", "aria-label": "", "type": "url", "instructions": "", "required": 1, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "parent_repeater": "field_global_info_links"}, {"key": "field_global_info_link_target", "label": "Open in New Tab", "name": "link_target", "aria-label": "", "type": "true_false", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "message": "", "default_value": 0, "ui": 1, "ui_on_text": "Yes", "ui_off_text": "No", "parent_repeater": "field_global_info_links"}]}, {"key": "field_global_info_anchor", "label": "HTML Anchor", "name": "anchor", "aria-label": "", "type": "text", "instructions": "Enter a custom HTML anchor ID (optional)", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "#", "append": "", "maxlength": ""}, {"key": "field_68275545b86d1", "label": "Map", "name": "map", "aria-label": "", "type": "image", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "return_format": "array", "library": "all", "min_width": "", "min_height": "", "min_size": "", "max_width": "", "max_height": "", "max_size": "", "mime_types": "", "preview_size": "medium"}], "location": [[{"param": "block", "operator": "==", "value": "acf/global-info-block"}]], "menu_order": 0, "position": "normal", "style": "default", "label_placement": "top", "instruction_placement": "label", "hide_on_screen": "", "active": true, "description": "", "show_in_rest": 0, "modified": 1747408207}