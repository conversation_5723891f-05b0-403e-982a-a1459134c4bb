{"key": "group_partners_block", "title": "Partners Block", "fields": [{"key": "field_partners_block_title", "label": "Title", "name": "title", "type": "text", "instructions": "Enter a title for the partners section", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "Partners", "placeholder": "", "prepend": "", "append": "", "maxlength": ""}, {"key": "field_partners_block_description", "label": "Description", "name": "description", "type": "textarea", "instructions": "Enter an optional description for the partners section", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "maxlength": "", "rows": 3, "new_lines": "br"}, {"key": "field_partners_block_columns", "label": "Columns", "name": "columns", "type": "number", "instructions": "Number of columns to display partners in", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": 4, "min": 1, "max": 6, "step": 1, "placeholder": ""}, {"key": "field_partners_block_partners", "label": "Partners", "name": "partners", "type": "repeater", "instructions": "Add partners to display in this section", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "collapsed": "field_partners_block_partner_name", "min": 0, "max": 0, "layout": "table", "button_label": "Add Partner", "sub_fields": [{"key": "field_partners_block_partner_name", "label": "Partner Name", "name": "partner_name", "type": "text", "instructions": "", "required": 1, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": ""}, {"key": "field_partners_block_partner_url", "label": "Partner URL", "name": "partner_url", "type": "url", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": ""}, {"key": "field_partners_block_partner_image", "label": "Partner Image", "name": "partner_image", "type": "image", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "return_format": "array", "preview_size": "medium", "library": "all", "min_width": "", "min_height": "", "min_size": "", "max_width": "", "max_height": "", "max_size": "", "mime_types": ""}]}], "location": [[{"param": "block", "operator": "==", "value": "acf/partners-block"}]], "menu_order": 0, "position": "normal", "style": "default", "label_placement": "top", "instruction_placement": "label", "hide_on_screen": "", "active": true, "description": "", "show_in_rest": 0}