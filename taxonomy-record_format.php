<?php
/**
 * The template for displaying record format archives
 */

get_header();

$term = get_queried_object();
?>

<div class="recordsArchive">
    <div class="contentWrapper">
        <h1 class="bigTitle"><?php echo esc_html($term->name); ?></h1>

        <?php if (!empty($term->description)) : ?>
            <div class="termDescription">
                <?php echo wpautop($term->description); ?>
            </div>
        <?php endif; ?>

        <div class="recordFilters">
            <div class="filterGroup">
                <label for="artist-filter"><?php _e('Artist:', 'rewindrecords'); ?></label>
                <select id="artist-filter">
                    <option value=""><?php _e('All Artists', 'rewindrecords'); ?></option>
                    <?php
                    $artists = get_terms(array(
                        'taxonomy' => 'record_artist',
                        'hide_empty' => true,
                    ));

                    foreach ($artists as $artist) {
                        echo '<option value="' . esc_attr($artist->slug) . '">' . esc_html($artist->name) . '</option>';
                    }
                    ?>
                </select>
            </div>

            <div class="filterGroup">
                <label for="genre-filter"><?php _e('Genre:', 'rewindrecords'); ?></label>
                <select id="genre-filter">
                    <option value=""><?php _e('All Genres', 'rewindrecords'); ?></option>
                    <?php
                    $genres = get_terms(array(
                        'taxonomy' => 'record_genre',
                        'hide_empty' => true,
                    ));

                    foreach ($genres as $genre) {
                        echo '<option value="' . esc_attr($genre->slug) . '">' . esc_html($genre->name) . '</option>';
                    }
                    ?>
                </select>
            </div>
        </div>

        <div class="recordsGrid">
            <?php
            if (have_posts()) :
                while (have_posts()) : the_post();
                    rewindrecords_render_record_item(get_the_ID(), array(
                        'image_size' => 'record-cover-optimized',
                        'show_artist' => false,
                        'show_year' => true,
                        'show_format' => true, // Don't show format on format archive page
                        'show_price' => true,
                        'lazy_load' => false, // No lazy loading on initial page load
                    ));
                endwhile;
                ?>
                <div class="pagination">
                    <?php
                    echo paginate_links(array(
                        'prev_text' => '<i class="icon-arrow-left"></i> ' . __('Previous', 'rewindrecords'),
                        'next_text' => __('Next', 'rewindrecords') . ' <i class="icon-arrow-right"></i>',
                    ));
                    ?>
                </div>
            <?php else : ?>
                <div class="noRecords">
                    <p><?php _e('No records found.', 'rewindrecords'); ?></p>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<?php get_footer(); ?>
