<?php
/**
 * Block Name: Spotify Text Block
 * Description: A block with Spotify embed on one side and text content on the other
 */

// Get block values
$subtitle = get_field('subtitle');
$title = get_field('title');
$content = get_field('content');
$links = get_field('links');
$spotify_embed = get_field('spotify_embed');
$spotify_position = get_field('spotify_position') ?: 'left';
$background_color = get_field('background_color') ?: '#1A1A1A';

// Default Spotify embed if none provided
$default_spotify_embed = '<iframe style="border-radius:12px" src="https://open.spotify.com/embed/playlist/1eT7NutAzdeW0Yj8nOnjGF?utm_source=generator" width="100%" height="352" frameBorder="0" allowfullscreen="" allow="autoplay; clipboard-write; encrypted-media; fullscreen; picture-in-picture" loading="lazy"></iframe>';

// Use default if no embed provided
if (empty($spotify_embed)) {
    $spotify_embed = $default_spotify_embed;
}

// Block ID
$block_id = 'spotify-text-' . $block['id'];

// Block classes
$classes = 'spotifyTextBlock';
$classes .= ' spotify-' . $spotify_position;

if (!empty($block['className'])) {
    $classes .= ' ' . $block['className'];
}
if (!empty($block['align'])) {
    $classes .= ' align' . $block['align'];
}
?>

<section id="<?php echo esc_attr($block_id); ?>" class="<?php echo esc_attr($classes); ?>" data-init <?php if (get_field("anchor")): ?>data-anchor="<?php the_field("anchor") ?>"<?php endif; ?>>
    <div class="contentWrapper">
        <div class="col textCol">
            <?php if ($subtitle) : ?>
                <div class="normalTitle primary" data-lines data-words><?php echo esc_html($subtitle); ?></div>
            <?php endif; ?>
            <?php if ($title) : ?>
                <h2 class="bigTitle" data-lines data-words><?php echo esc_html($title); ?></h2>
            <?php endif; ?>
            
            <?php if ($content) : ?>
                <div class="text" data-lines data-words><?php echo $content; ?></div>
            <?php endif; ?>
            
            <?php if ($links) : ?>
                <div class="links">
                    <?php foreach ($links as $link) : ?>
                        <a href="<?php echo esc_url($link['link_url']); ?>" class="textLink" <?php echo $link['link_target'] ? 'target="_blank" rel="noopener noreferrer"' : ''; ?>>
                            <i class="icon-arrow-right"></i>
                            <span class="innerMask">
                                <span class="innerWrapper">
                                    <span class="innerText absolute"><?php echo esc_html($link['link_text']); ?></span>
                                    <span class="innerText absolute" aria-hidden="true"><?php echo esc_html($link['link_text']); ?></span>
                                </span>
                            </span>
                            <span class="divider">
                                <svg xmlns="http://www.w3.org/2000/svg" width="184.5" height="2" viewBox="0 0 184.5 2">
                                    <line data-name="Line 6" x2="184.5" transform="translate(0 1)" fill="none" stroke="#eee" stroke-width="2"/>
                                </svg>
                            </span>
                        </a>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
        </div>
        <div class="col spotify">
            <div class="spotifyWrapper">
                <?php echo $spotify_embed; ?>
            </div>
        </div>
    </div>
</section>
