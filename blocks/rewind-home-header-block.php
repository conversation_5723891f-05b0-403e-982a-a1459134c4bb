<?php
/**
 * Block Name: Rewind Home Header Block
 * Description: A header block with title, text, location, links, and media (image or video)
 */

// Get block values
$title = get_field('title');
$text = get_field('text');
$media_type = get_field('media_type') ?: 'image';
$image = get_field('image');
$video_url = get_field('video_url');
$show_location = get_field('show_location');
$links = get_field('links');

// Get location information if enabled
$location = null;
if ($show_location) {
    $location = rewindrecords_get_location();
}

// Block ID
$block_id = 'rewind-home-header-' . $block['id'];

// Block classes
$classes = 'rewind-home-header-block';
if (!empty($block['className'])) {
    $classes .= ' ' . $block['className'];
}
if (!empty($block['align'])) {
    $classes .= ' align' . $block['align'];
}
?>

<section id="<?php echo esc_attr($block_id); ?>" class="<?php echo esc_attr($classes); ?>" data-init <?php if (get_field("anchor")): ?>data-anchor="<?php the_field("anchor") ?>"<?php endif; ?>>
    <div class="contentWrapper">
        <div class="rewind-home-header-content">
            <div class="rewind-home-header-left">
                <div class="rewind-home-header-info">
                    <?php // Titel ?>
                    <?php if ($title) : ?>
                        <h1 class="rewind-home-header-title"><?php echo esc_html($title); ?></h1>
                    <?php endif; ?>

                    <?php // Locatie ?>
                    <?php if ($location && !empty($location['formatted_address'])) : ?>
                        <div class="rewind-home-header-location">
                            <i class="fas fa-map-marker-alt"></i>
                            <?php if (!empty($location['maps_link'])) : ?>
                                <a href="<?php echo esc_url($location['maps_link']); ?>" target="_blank" rel="noopener noreferrer">
                                    <?php echo esc_html($location['formatted_address']); ?>
                                </a>
                            <?php else : ?>
                                <span><?php echo esc_html($location['formatted_address']); ?></span>
                            <?php endif; ?>
                        </div>
                    <?php endif; ?>

                    <?php // Tekst ?>
                    <?php if ($text) : ?>
                        <div class="rewind-home-header-text"><?php echo wpautop($text); ?></div>
                    <?php endif; ?>

                    <?php // Links ?>
                    <?php if ($links) : ?>
                        <div class="rewind-home-header-links">
                            <?php foreach ($links as $link) : ?>
                                <a href="<?php echo esc_url($link['link_url']); ?>" class="rewind-home-header-link" <?php echo $link['link_target'] ? 'target="_blank" rel="noopener noreferrer"' : ''; ?>>
                                    <span class="link-text"><?php echo esc_html($link['link_text']); ?></span>
                                    <i class="icon-arrow-right"></i>
                                </a>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <div class="rewind-home-header-right">
                <div class="rewind-home-header-media">
                    <?php if ($media_type === 'image' && $image) : ?>
                        <div class="rewind-home-header-image">
                            <img src="<?php echo esc_url($image['url']); ?>" alt="<?php echo esc_attr($image['alt']); ?>" />
                        </div>
                    <?php elseif ($media_type === 'video' && $video_url) : ?>
                        <div class="rewind-home-header-video">
                            <video autoplay muted loop playsinline>
                                <source src="<?php echo esc_url($video_url); ?>" type="video/mp4">
                                <?php _e('Your browser does not support the video tag.', 'rewindrecords'); ?>
                            </video>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</section>
