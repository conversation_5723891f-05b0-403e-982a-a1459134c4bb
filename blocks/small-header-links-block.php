<?php
/**
 * Block Name: Small Header Links Block
 * Description: A centered header with a huge title and multiple links
 */

// Get block values
$title = get_field('title');
$links = get_field('links');

// Block ID
$block_id = 'small-header-links-' . $block['id'];

// Block classes
$classes = 'smallHeaderLinksBlock';

if (!empty($block['className'])) {
    $classes .= ' ' . $block['className'];
}
if (!empty($block['align'])) {
    $classes .= ' align' . $block['align'];
}
?>

<section id="<?php echo esc_attr($block_id); ?>" class="<?php echo esc_attr($classes); ?>" data-init <?php if (get_field("anchor")): ?>data-anchor="<?php the_field("anchor") ?>"<?php endif; ?>>
    <div class="contentWrapper smaller">
        <div class="innerCol" data-parallax data-parallax-speed="2">
            <?php if ($title) : ?>
                <h1 class="hugeTitle" data-lines data-words><?php echo esc_html($title); ?></h1>
            <?php endif; ?>
            
            <?php if ($links) : ?>
                <div class="links">
                    <?php foreach ($links as $link) : ?>
                        <a href="<?php echo esc_url($link['link_url']); ?>" class="textLink" <?php echo $link['link_target'] ? 'target="_blank" rel="noopener noreferrer"' : ''; ?>>
                            <i class="icon-arrow-right"></i>
                            <span class="innerMask">
                                <span class="innerWrapper">
                                    <span class="innerText absolute"><?php echo esc_html($link['link_text']); ?></span>
                                    <span class="innerText absolute" aria-hidden="true"><?php echo esc_html($link['link_text']); ?></span>
                                </span>
                            </span>
                            <span class="divider">
                                <svg xmlns="http://www.w3.org/2000/svg" width="184.5" height="2" viewBox="0 0 184.5 2">
                                    <line data-name="Line 6" x2="184.5" transform="translate(0 1)" fill="none" stroke="#eee" stroke-width="2"/>
                                </svg>
                            </span>
                        </a>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
        </div>
    </div>
</section>
