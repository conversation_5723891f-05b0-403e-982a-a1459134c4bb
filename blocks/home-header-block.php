<?php
/**
 * Block Name: Media Text Block
 * Description: A block with media (image or video) on one side and text content on the other
 */

// Get block values
$title = get_field('title');
$content = get_field('text');
$links = get_field('links');
$media_type = get_field('media_type') ?: 'image';
$image = get_field('image');
$video_url = get_field('video_url');
$media_position = get_field('media_position') ?: 'left';
$background_color = get_field('background_color') ?: '#1A1A1A';

// Block ID
$block_id = 'media-text-' . $block['id'];

if (!empty($block['className'])) {
    $classes .= ' ' . $block['className'];
}
if (!empty($block['align'])) {
    $classes .= ' align' . $block['align'];
}
?>

<section id="<?php echo esc_attr($block_id); ?>" class="homeHeaderBlock" data-init <?php if (get_field("anchor")): ?>data-anchor="<?php the_field("anchor") ?>"<?php endif; ?>>
    <div class="contentWrapper">
        <div class="cols">
            <div class="col" data-parallax data-parallax-speed="2">
                <?php if ($title) : ?>
                    <div class="titleWrapper">
                        <h1 class="bigTitle" data-lines data-words><?php echo esc_html($title); ?></h1>
                        <a href="<?php echo esc_url(get_theme_mod('customTheme-main-callout-maps')); ?>" target="_blank" title="Routebeschrijving" class="textLink">
                            <i class="icon-marker"></i>
                            <span class="innerMask">
                                <span class="innerWrapper">
                                    <span class="innerText absolute">Eindhoven</span>
                                    <span class="innerText absolute" aria-hidden="true">Eindhoven</span>
                                </span>
                            </span>
                            <span class="divider">
                                <svg xmlns="http://www.w3.org/2000/svg" width="184.5" height="2" viewBox="0 0 184.5 2">
                                    <line data-name="Line 6" x2="184.5" transform="translate(0 1)" fill="none" stroke="#eee" stroke-width="2"/>
                                </svg>
                            </span>
                        </a>
                    </div>
                <?php endif; ?>

                <?php if ($content) : ?>
                    <div class="text">
                        <p data-lines data-words><?php the_field('text'); ?></p>
                    </div>
                <?php endif; ?>

                <?php if ($links) : ?>
                    <div class="links">
                        <?php foreach ($links as $link) : ?>
                            <a href="<?php echo esc_url($link['link_url']); ?>" title="<?php echo esc_html($link['link_text']); ?>" class="textLink" <?php echo $link['link_target'] ? 'target="_blank" rel="noopener noreferrer"' : ''; ?>>
                                <i class="icon-arrow-right"></i>
                                <span class="innerMask">
                                    <span class="innerWrapper">
                                        <span class="innerText absolute"><?php echo esc_html($link['link_text']); ?></span>
                                        <span class="innerText absolute" aria-hidden="true"><?php echo esc_html($link['link_text']); ?></span>
                                    </span>
                                </span>
                                <span class="divider">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="184.5" height="2" viewBox="0 0 184.5 2">
                                        <line data-name="Line 6" x2="184.5" transform="translate(0 1)" fill="none" stroke="#eee" stroke-width="2"/>
                                    </svg>
                                </span>
                            </a>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
            <div class="col">
                <?php if ($media_type === 'image' && $image) : ?>
                    <div class="imageWrapper">
                        <div class="innerImage">
                         <img src="<?php echo esc_url($image['url']); ?>" alt="<?php echo esc_attr($image['alt']); ?>" />
                        </div>
                    </div>
                <?php elseif ($media_type === 'video' && $video_url) : ?>
                    <div class="media-video">
                        <video autoplay muted loop playsinline>
                            <source src="<?php echo esc_url($video_url); ?>" type="video/mp4">
                            <?php _e('Your browser does not support the video tag.', 'rewindrecords'); ?>
                        </video>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</section>
