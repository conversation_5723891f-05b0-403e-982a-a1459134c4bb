<?php
/**
 * Partners Block Template
 */

// Get block attributes
$title = get_field('title') ?: __('Partners', 'rewindrecords');
$description = get_field('description');
$columns = get_field('columns') ?: 4;
$partners = get_field('partners');
$anchor = get_field('anchor');

// Set column class based on number of columns
$column_class = 'col-' . $columns;
?>

<section class="partnersBlock" data-init <?php if ($anchor): ?>data-anchor="<?php echo esc_attr($anchor); ?>"<?php endif; ?>>
    <div class="contentWrapper">
        <?php if ($title): ?>
            <h2 class="biggerTitle"><?php echo esc_html($title); ?></h2>
        <?php endif; ?>
        
        <?php if ($description): ?>
            <div class="description">
                <?php echo wp_kses_post($description); ?>
            </div>
        <?php endif; ?>
        
        <?php if ($partners && count($partners) > 0): ?>
            <div class="partnersGrid <?php echo esc_attr($column_class); ?>">
                <?php foreach ($partners as $partner): ?>
                    <div class="partnerItem">
                        <?php if (!empty($partner['partner_url'])): ?>
                            <a href="<?php echo esc_url($partner['partner_url']); ?>" title="<?php echo esc_attr($partner['partner_name']); ?>" target="_blank" rel="noopener">
                        <?php endif; ?>
                        
                        <?php if (!empty($partner['partner_image'])): ?>
                            <div class="partnerImageWrapper">
                                <img class="lazy" data-src="<?php echo esc_url($partner['partner_image']['url']); ?>" alt="<?php echo esc_attr($partner['partner_name']); ?>">
                            </div>
                        <?php endif; ?>
                        
                        <div class="partnerName">
                            <?php echo esc_html($partner['partner_name']); ?>
                        </div>
                        
                        <?php if (!empty($partner['partner_url'])): ?>
                            </a>
                        <?php endif; ?>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    </div>
</section>
