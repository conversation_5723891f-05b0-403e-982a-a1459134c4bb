<?php
/**
 * Contact Block with Random Record Covers
 */

// Get random records for the covers
$random_records_left = new WP_Query(array(
    'post_type' => 'record',
    'posts_per_page' => 2,
    'orderby' => 'rand',
    'tax_query' => array(
        'relation' => 'AND',
        // Only show albums
        array(
            'taxonomy' => 'record_product_type',
            'field' => 'slug',
            'terms' => 'album',
        ),
        // Exclude records with format 'Spellen' or other non-music formats
        array(
            'taxonomy' => 'record_format',
            'field' => 'slug',
            'terms' => array('spellen', 'bordspellen', 'games', 'board-games'),
            'operator' => 'NOT IN',
        ),
    ),
));

$random_records_right = new WP_Query(array(
    'post_type' => 'record',
    'posts_per_page' => 2,
    'orderby' => 'rand',
    'post__not_in' => wp_list_pluck($random_records_left->posts, 'ID'), // Exclude records already used
    'tax_query' => array(
        'relation' => 'AND',
        // Only show albums
        array(
            'taxonomy' => 'record_product_type',
            'field' => 'slug',
            'terms' => 'album',
        ),
        // Exclude records with format 'Spellen' or other non-music formats
        array(
            'taxonomy' => 'record_format',
            'field' => 'slug',
            'terms' => array('spellen', 'bordspellen', 'games', 'board-games'),
            'operator' => 'NOT IN',
        ),
    ),
));
?>

<section class="contactBlock" data-init <?php if (get_field("anchor")): ?>data-anchor="<?php the_field("anchor") ?>"<?php endif; ?>>
    <div class="cols">
        <div class="col" data-parallax data-parallax-speed="-1">
            <div class="images">
                <?php if ($random_records_left->have_posts()) : $i = 1; while ($random_records_left->have_posts() && $i <= 2) : $random_records_left->the_post(); ?>
                    <div class="imageWrapper">
                        <div class="innerImage">
                            <?php if (has_post_thumbnail()) : ?>
                                <a href="<?php the_permalink(); ?>" title="<?php the_title_attribute(); ?>">
                                    <img class="lazy" data-src="<?php echo esc_url(get_the_post_thumbnail_url(get_the_ID(), 'large')); ?>" alt="<?php the_title_attribute(); ?>" />
                                </a>
                            <?php else : ?>
                                <a href="<?php the_permalink(); ?>" title="<?php the_title_attribute(); ?>">
                                    <div class="defaultCover">
                                        <svg viewBox="0 0 512 512" xmlns="http://www.w3.org/2000/svg">
                                            <circle cx="256" cy="256" r="240" fill="#333" />
                                            <circle cx="256" cy="256" r="120" fill="#F5D042" />
                                            <circle cx="256" cy="256" r="30" fill="#333" />
                                        </svg>
                                    </div>
                                </a>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php $i++; endwhile; wp_reset_postdata(); endif; ?>
            </div>
        </div>

        <div class="col">
            <div class="formWrapper">
                <div class="formIntro">
                    <h2 class="normalTitle" data-lines data-words><?php the_field("title"); ?></h2>
                    <div class="text"><p data-lines data-words><?php the_field('text'); ?></p></div>
                </div>
                <div class="contactFormWrapper" data-swup-form>
                    <?php echo(the_field("form")); ?>
                </div>
            </div>
        </div>

        <div class="col" data-parallax data-parallax-speed="-1">
            <div class="images">
                <?php if ($random_records_right->have_posts()) : $i = 1; while ($random_records_right->have_posts() && $i <= 2) : $random_records_right->the_post(); ?>
                    <div class="imageWrapper">
                        <div class="innerImage">
                            <?php if (has_post_thumbnail()) : ?>
                                <a href="<?php the_permalink(); ?>" title="<?php the_title_attribute(); ?>">
                                    <img class="lazy" data-src="<?php echo esc_url(get_the_post_thumbnail_url(get_the_ID(), 'large')); ?>" alt="<?php the_title_attribute(); ?>" />
                                </a>
                            <?php else : ?>
                                <a href="<?php the_permalink(); ?>" title="<?php the_title_attribute(); ?>">
                                    <div class="defaultCover">
                                        <svg viewBox="0 0 512 512" xmlns="http://www.w3.org/2000/svg">
                                            <circle cx="256" cy="256" r="240" fill="#333" />
                                            <circle cx="256" cy="256" r="120" fill="#F5D042" />
                                            <circle cx="256" cy="256" r="30" fill="#333" />
                                        </svg>
                                    </div>
                                </a>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php $i++; endwhile; wp_reset_postdata(); endif; ?>
            </div>
        </div>
    </div>
</section>
