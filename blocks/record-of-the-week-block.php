<?php
/**
 * Block Name: Record of the Week
 * Description: Display a featured record of the week, either from your collection or manually entered
 */

// Get block values
$block_title = get_field('block_title') ?: 'Record of the Week';
$record_source = get_field('record_source') ?: 'collection';
$description = get_field('description');
$background_color = get_field('background_color') ?: '#1A1A1A';
$vinyl_image = get_field('vinyl_image');
// Variables for record data
$record_title = '';
$record_artist = '';
$record_image = null;
$record_link = '';
$has_link = false;

// Get record data based on source
if ($record_source === 'collection') {
    $selected_record = get_field('selected_record');

    if ($selected_record) {
        // Check if the selected record is an album and not a game
        $is_album = false;
        $is_game = false;

        // Check product type
        $product_types = get_the_terms($selected_record->ID, 'record_product_type');
        if ($product_types && !is_wp_error($product_types)) {
            foreach ($product_types as $type) {
                if ($type->slug === 'album') {
                    $is_album = true;
                    break;
                }
            }
        }

        // Check format to exclude games
        $formats = get_the_terms($selected_record->ID, 'record_format');
        if ($formats && !is_wp_error($formats)) {
            foreach ($formats as $format) {
                if (in_array($format->slug, array('spellen', 'bordspellen', 'games', 'board-games'))) {
                    $is_game = true;
                    break;
                }
            }
        }

        // If no product type is set, assume it's an album (for backward compatibility)
        if (empty($product_types) || is_wp_error($product_types)) {
            $is_album = true;
        }

        // Only proceed if it's an album and not a game
        $is_valid_record = $is_album && !$is_game;

        if ($is_valid_record) {
            $record_title = $selected_record->post_title;

            // Get artist
            $artists = get_the_terms($selected_record->ID, 'record_artist');
            if ($artists && !is_wp_error($artists)) {
                $record_artist = $artists[0]->name;
            }

            // Get image
            if (has_post_thumbnail($selected_record->ID)) {
                $record_image = get_the_post_thumbnail_url($selected_record->ID, 'record-cover-optimized');
            }

            // Get link
            $enable_link = get_field('link');
            if ($enable_link) {
                $record_link = get_permalink($selected_record->ID);
                $has_link = true;
            }
        }
    }
} else {
    // Manual entry
    $record_title = get_field('manual_title');
    $record_artist = get_field('manual_artist');

    $manual_image = get_field('manual_image');
    if ($manual_image) {
        $record_image = $manual_image['url'];
    }

    $manual_link = get_field('manual_link');
    if ($manual_link) {
        $record_link = $manual_link;
        $has_link = true;
    }
}

// Block ID
$block_id = 'record-of-the-week-' . $block['id'];

// Block classes
$classes = 'recordOfTheWeekBlock';
if (!empty($block['className'])) {
    $classes .= ' ' . $block['className'];
}
if (!empty($block['align'])) {
    $classes .= ' align' . $block['align'];
}
?>

<section data-init id="<?php echo esc_attr($block_id); ?>" class="<?php echo esc_attr($classes); ?>" style="background-color: <?php echo esc_attr($background_color); ?>">
    <div class="contentWrapper small">
        <div class="col" data-parallax data-parallax-speed="2">
            <div class="imageWrapper">
                <div class="vinyl">
                    <img class="lazy" data-src="<?php echo esc_url($vinyl_image['sizes']['medium']); ?>" alt="<?php echo esc_attr($vinyl_image['alt']); ?>" />
                </div>
                <div class="innerImage">
                    <?php if ($record_image) : ?>
                        <?php if ($has_link) : ?>
                            <img class="lazy" data-src="<?php echo esc_url($record_image); ?>" alt="<?php echo esc_attr($record_title); ?>">
                        <?php else : ?>
                            <img class="lazy" data-src="<?php echo esc_url($record_image); ?>" alt="<?php echo esc_attr($record_title); ?>">
                        <?php endif; ?>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        <div class="col">
            <?php if ($record_title) : ?>
                <h2 class="normalTitle" data-lines data-words>
                    <span class="primary"><?php echo esc_html($block_title); ?></span></br>
                    <?php echo esc_html($record_title); ?>
                </h2>
            <?php endif; ?>

            <?php if ($description) : ?>
                <div class="text" data-lines data-words><?php echo $description; ?></div>
            <?php endif; ?>
            <div class="signature">
                <svg viewBox="0 0 113.38 64.5" class="signature-svg">
                    <g>
                        <path class="signature-path" d="M1.23,44.6h7.8c.33,0,.********-.2.47-.47.7-.8.7H.53c-.53,0-.67-.3-.4-.9.27-.47.63-.7,1.1-.7Z"/>
                        <path class="signature-path" d="M18.23,64.5c-5.33,0-8-1.67-8-5,0-2.27,1.15-4.47,3.45-6.6,2.3-2.13,5.08-3.6,8.35-4.4.27-.*********.**********.48.2.75-.07.27-.23.43-.5.5-2.87.73-5.33,2.03-7.4,3.9-2.07,1.87-3.1,3.73-3.1,5.6,0,2.33,2.1,3.5,6.3,3.5,5.4,0,10.53-1.73,15.4-5.2,4.53-3.13,6.8-6.53,6.8-10.2,0-2.33-1-4.23-3-5.7-1.47-1.2-3.6-2.6-6.4-4.2-3.13-1.73-5.63-3.4-7.5-5-2.47-2.27-3.7-4.9-3.7-7.9,0-3.27,1.8-6.77,5.4-10.5,3.8-3.8,8.6-7.03,14.4-9.7C46.03,1.53,51.9,0,57.23,0s7.7,1.77,7.7,5.3c0,5.47-4.53,11.8-13.6,19-.2.2-.45.25-.75.15-.3-.1-.48-.28-.55-.55-.07-.27,0-.53.2-.8,8.73-6.93,13.1-12.87,13.1-17.8,0-2.47-2.03-3.7-6.1-3.7-4.8,0-10.43,1.47-16.9,4.4-5.67,2.6-10.23,5.63-13.7,9.1-3.47,3.47-5.2,6.7-5.2,9.7,0,1.8.57,3.47,1.7,5,1.6,2.07,4.5,4.2,8.7,6.4,3.2,1.8,5.5,3.33,6.9,4.6,2.27,1.8,3.4,4.07,3.4,6.8,0,4.2-2.5,8.03-7.5,11.5-5.07,3.6-10.53,5.4-16.4,5.4Z"/>
                        <path class="signature-path" d="M85.73,43.6c.33-.27.68-.22,1.05.15.37.37.38.72.05,1.05-3.73,2.8-7.63,5.17-11.7,7.1-4.2,2-7.43,3-9.7,3-2.07,0-3.1-1.03-3.1-3.1,0-1.4.53-3.07,1.6-5-4.73,3.27-7.87,4.9-9.4,4.9s-2.4-.7-2.4-2.1c0-1.6,1.57-3.9,4.7-6.9.33-.33.67-.3,1,.1.33.4.4.73.2,1-2.8,2.73-4.2,4.67-4.2,5.8,0,.33.23.5.7.5.73,0,2.77-1,6.1-3,3.47-2.2,5.5-3.73,6.1-4.6.33-.33.7-.33,1.1,0,.4.33.47.67.2,1v.1l-.1.1c-2.67,3.2-4,5.9-4,8.1,0,1,.5,1.5,1.5,1.5,2.27,0,5.3-.93,9.1-2.8,3.93-1.87,7.67-4.17,11.2-6.9Z"/>
                        <path class="signature-path" d="M80.53,54.4c-.27.2-.55.3-.85.3s-.52-.1-.65-.3c-.13-.2-.13-.47,0-.8.13-.13.33-.45.6-.95.27-.5.47-.82.6-.95,1.93-3.4,4.17-6.53,6.7-9.4.27-.53.6-.53,1,0,.27.33.33.67.2,1-2.13,2.53-4.03,5.13-5.7,7.8,4.47-3.67,7.73-5.5,9.8-5.5.53,0,1.02.2,1.45.6.43.4.65.87.65,1.4,0,.27-.13.9-.4,1.9-.27,1-.4,1.63-.4,1.9-.07.33,0,.58.2.75.2.17.5.25.9.25,1.53,0,4.23-1.03,8.1-3.1,3.8-2.07,6.93-4,9.4-5.8.33-.27.67-.2,1,.2.33.4.33.77,0,1.1-2.47,1.73-5.73,3.7-9.8,5.9-4.13,2.2-7.03,3.3-8.7,3.3-2,0-2.93-.93-2.8-2.8,0-.2.13-.78.4-1.75.27-.96.4-1.58.4-1.85s-.17-.4-.5-.4c-1.87,0-5.73,2.4-11.6,7.2Z"/>
                    </g>
                </svg>
            </div>
        </div>
    </div>
</section>
