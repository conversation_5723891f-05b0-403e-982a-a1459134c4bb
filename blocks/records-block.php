<?php
/**
 * Records Block Template
 */

// Get block attributes
$number = get_field('number') ?: 6;
$columns = get_field('columns') ?: 3;
$genre = get_field('genre');
$artist = get_field('artist');
$format = get_field('format');
$show_filters = get_field('show_filters') ?: false;
$title = get_field('title') ?: __('Latest Records', 'rewindrecords');
$view_all_link = get_field('view_all_link') ?: get_post_type_archive_link('record');

// Query args
$args = array(
    'post_type' => 'record',
    'posts_per_page' => $number,
    'meta_key' => '_record_release_date',
    'orderby' => 'meta_value',
    'order' => 'DESC',
    'tax_query' => array(
        'relation' => 'AND',
        // Only show albums
        array(
            'taxonomy' => 'record_product_type',
            'field' => 'slug',
            'terms' => 'album',
        ),
        // Exclude records with format 'Spellen' or other non-music formats
        array(
            'taxonomy' => 'record_format',
            'field' => 'slug',
            'terms' => array('spellen', 'bordspellen', 'games', 'board-games'),
            'operator' => 'NOT IN',
        ),
    ),
);

// Add taxonomy queries if set
// Start with the existing product type filter
$tax_query = $args['tax_query'];

if ($genre) {
    $tax_query[] = array(
        'taxonomy' => 'record_genre',
        'field' => 'term_id',
        'terms' => $genre,
    );
}

if ($artist) {
    $tax_query[] = array(
        'taxonomy' => 'record_artist',
        'field' => 'term_id',
        'terms' => $artist,
    );
}

if ($format) {
    $tax_query[] = array(
        'taxonomy' => 'record_format',
        'field' => 'term_id',
        'terms' => $format,
    );
}

// Update the tax_query in the args
$args['tax_query'] = $tax_query;

// Run the query
$records_query = new WP_Query($args);
?>

<section class="recordsBlock" data-init <?php if (get_field("anchor")): ?>data-anchor="<?php the_field("anchor"); ?>"<?php endif; ?>>
    <div class="contentWrapper">
        <div class="recordsBlockHeader">
            <h2 class="biggerTitle white"><?php echo esc_html($title); ?></h2>

            <?php if ($show_filters && !$genre && !$artist && !$format): ?>
                <div class="recordFilters">
                    <div class="filterGroup">
                        <label for="genre-filter-block"><?php _e('Genre:', 'rewindrecords'); ?></label>
                        <select id="genre-filter-block" class="recordFilter">
                            <option value=""><?php _e('All Genres', 'rewindrecords'); ?></option>
                            <?php
                            $genres = get_terms(array(
                                'taxonomy' => 'record_genre',
                                'hide_empty' => true,
                            ));

                            foreach ($genres as $term) {
                                echo '<option value="' . esc_attr($term->slug) . '">' . esc_html($term->name) . '</option>';
                            }
                            ?>
                        </select>
                    </div>

                    <div class="filterGroup">
                        <label for="artist-filter-block"><?php _e('Artist:', 'rewindrecords'); ?></label>
                        <select id="artist-filter-block" class="recordFilter">
                            <option value=""><?php _e('All Artists', 'rewindrecords'); ?></option>
                            <?php
                            $artists = get_terms(array(
                                'taxonomy' => 'record_artist',
                                'hide_empty' => true,
                            ));

                            foreach ($artists as $term) {
                                echo '<option value="' . esc_attr($term->slug) . '">' . esc_html($term->name) . '</option>';
                            }
                            ?>
                        </select>
                    </div>
                </div>

                <script>
                    jQuery(document).ready(function($) {
                        $('.recordFilter').on('change', function() {
                            var genre = $('#genre-filter-block').val();
                            var artist = $('#artist-filter-block').val();

                            var url = '<?php echo esc_url(get_post_type_archive_link('record')); ?>';
                            var params = [];

                            if (genre) {
                                params.push('record_genre=' + genre);
                            }

                            if (artist) {
                                params.push('record_artist=' + artist);
                            }

                            if (params.length > 0) {
                                url += '?' + params.join('&');
                            }

                            window.location.href = url;
                        });
                    });
                </script>
            <?php endif; ?>
        </div>

        <?php if ($records_query->have_posts()): ?>
            <div class="recordsGrid" style="grid-template-columns: repeat(<?php echo esc_attr($columns); ?>, 1fr);">
                <?php
                while ($records_query->have_posts()): $records_query->the_post();
                    rewindrecords_render_record_item(get_the_ID(), array(
                        'image_size' => 'record-cover-optimized',
                        'show_artist' => true,
                        'show_year' => true,
                        'show_format' => false,
                        'show_price' => true,
                        'lazy_load' => true,
                    ));
                endwhile;
                ?>
            </div>

            <?php if ($view_all_link): ?>
                <div class="viewAllLink">
                    <a href="<?php echo esc_url($view_all_link); ?>" class="button">
                        <?php _e('View All Records', 'rewindrecords'); ?>
                        <i class="icon-arrow-right"></i>
                    </a>
                </div>
            <?php endif; ?>
        <?php else: ?>
            <div class="noRecords">
                <p><?php _e('No records found. Import your collection first.', 'rewindrecords'); ?></p>
                <?php if (current_user_can('manage_options')): ?>
                    <a href="<?php echo admin_url('admin.php?page=rewindrecords-import'); ?>" class="button"><?php _e('Import Collection', 'rewindrecords'); ?></a>
                <?php endif; ?>
            </div>
        <?php endif; ?>

        <?php wp_reset_postdata(); ?>
    </div>
</section>
