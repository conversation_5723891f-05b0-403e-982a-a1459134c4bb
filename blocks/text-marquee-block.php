<?php
    $items = get_field('items');
?>
<section class="textMarqueeBlock <?php the_field("background") ?>" data-init <?php if (get_field("anchor")): ?>data-anchor="<?php the_field("anchor") ?>"<?php endif; ?>>
    <div class="marqueeWrapper" data-init>
        <div class="marquee" data-marquee data-marquee-direction="right" data-marquee-speed="25" data-marquee-scroll-speed="5" data-marquee-swipe="true">
            <?php if( $items ): ?>
                <?php if( count($items) > 1 ): ?>
                    <div class="marqueeScroll">
                        <div class="itemsContainer">
                            <?php foreach( $items as $item ): ?>
                                <h3 class="item normalTitle">
                                    <?php echo esc_html($item['text']); ?>&nbsp; -- &nbsp;
                                </h3>
                            <?php endforeach; ?>
                        </div>
                        <div class="itemsContainer">
                            <?php foreach( $items as $item ): ?>
                                <h3 class="item normalTitle">
                                    <?php echo esc_html($item['text']); ?>&nbsp; -- &nbsp;
                                </h3>
                            <?php endforeach; ?>
                        </div>
                        <div class="itemsContainer">
                            <?php foreach( $items as $item ): ?>
                                <h3 class="item normalTitle">
                                    <?php echo esc_html($item['text']); ?>&nbsp; -- &nbsp;
                                </h3>
                            <?php endforeach; ?>
                        </div>
                        <div class="itemsContainer">
                            <?php foreach( $items as $item ): ?>
                                <h3 class="item normalTitle">
                                    <?php echo esc_html($item['text']); ?>&nbsp; -- &nbsp;
                                </h3>
                            <?php endforeach; ?>
                        </div>
                    </div>
                <?php endif; ?>
            <?php endif; ?>
        </div>
    </div>
</section>
