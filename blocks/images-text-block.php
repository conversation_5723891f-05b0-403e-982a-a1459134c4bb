<?php
/**
 * Block Name: Images Text Block
 * Description: A block with three columns - image/video, text content with popup image, and image/video
 */

// Get block values
$title = get_field('title');
$content = get_field('content');
$links = get_field('links');
$popup_image = get_field('popup_image');
$middle_image = get_field('middle_image');

// Column 1 media
$col1_media_type = get_field('col1_media_type') ?: 'image';
$col1_image = get_field('col1_image');
$col1_video_url = get_field('col1_video_url');

// Column 3 media
$col3_media_type = get_field('col3_media_type') ?: 'image';
$col3_image = get_field('col3_image');
$col3_video_url = get_field('col3_video_url');

// Block ID
$block_id = 'images-text-' . $block['id'];

// Block classes
$classes = 'imagesTextBlock';

if (!empty($block['className'])) {
    $classes .= ' ' . $block['className'];
}
if (!empty($block['align'])) {
    $classes .= ' align' . $block['align'];
}
?>

<section id="<?php echo esc_attr($block_id); ?>" class="<?php echo esc_attr($classes); ?>" data-init <?php if (get_field("anchor")): ?>data-anchor="<?php the_field("anchor") ?>"<?php endif; ?>>
    <div class="contentWrapper">
        <div class="cols">
            <!-- Column 1: Image or Video -->
            <div class="col mediaCol">
                <div class="mediaWrapper">
                    <?php if ($col1_media_type === 'image' && $col1_image) : ?>
                        <div class="innerImage">
                            <img data-parallax data-parallax-speed="-1.5" class="lazy" data-src="<?php echo esc_url($col1_image['sizes']['medium_large']); ?>" alt="<?php echo esc_attr($col1_image['alt']); ?>" />
                        </div>
                    <?php elseif ($col1_media_type === 'video' && $col1_video_url) : ?>
                        <div class="innerImage">
                            <video autoplay muted loop playsinline>
                                <source src="<?php echo esc_url($col1_video_url); ?>" type="video/mp4">
                                <?php _e('Your browser does not support the video tag.', 'rewindrecords'); ?>
                            </video>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
            
            <!-- Column 2: Title, Text, Links, and Image -->
            <div class="col textCol">
                <div class="innerContent">
                    <?php if ($title) : ?>
                        <h2 class="normalTitle" data-lines data-words><?php echo esc_html($title); ?></h2>
                    <?php endif; ?>
                    
                    <?php if ($popup_image) : ?>
                        <div class="popupImageWrapper">
                            <img class="lazy" data-src="<?php echo esc_url($popup_image['sizes']['medium']); ?>" alt="<?php echo esc_attr($popup_image['alt']); ?>" />
                        </div>
                    <?php endif; ?>
                    
                    <?php if ($content) : ?>
                        <div class="text" data-lines data-words><?php echo $content; ?></div>
                    <?php endif; ?>
                    
                    <?php if ($links) : ?>
                        <div class="links">
                            <?php foreach ($links as $link) : ?>
                                <a href="<?php echo esc_url($link['link_url']); ?>" class="textLink" <?php echo $link['link_target'] ? 'target="_blank" rel="noopener noreferrer"' : ''; ?>>
                                    <i class="icon-arrow-right"></i>
                                    <span class="innerMask">
                                        <span class="innerWrapper">
                                            <span class="innerText absolute"><?php echo esc_html($link['link_text']); ?></span>
                                            <span class="innerText absolute" aria-hidden="true"><?php echo esc_html($link['link_text']); ?></span>
                                        </span>
                                    </span>
                                    <span class="divider">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="184.5" height="2" viewBox="0 0 184.5 2">
                                            <line data-name="Line 6" x2="184.5" transform="translate(0 1)" fill="none" stroke="#eee" stroke-width="2"/>
                                        </svg>
                                    </span>
                                </a>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
                <?php if ($middle_image) : ?>
                    <div class="middleImageWrapper">
                        <img class="middleImage lazy" data-src="<?php echo esc_url($middle_image['sizes']['medium_large']); ?>" alt="<?php echo esc_attr($middle_image['alt']); ?>" />
                    </div>
                <?php endif; ?>
            </div>
            
            <!-- Column 3: Image or Video -->
            <div class="col mediaCol">
                <div class="mediaWrapper">
                    <?php if ($col3_media_type === 'image' && $col3_image) : ?>
                        <div class="innerImage">
                            <img data-parallax data-parallax-speed="-1.5" class="lazy" data-src="<?php echo esc_url($col3_image['sizes']['medium_large']); ?>" alt="<?php echo esc_attr($col3_image['alt']); ?>" />
                        </div>
                    <?php elseif ($col3_media_type === 'video' && $col3_video_url) : ?>
                        <div class="innerImage">
                            <video autoplay muted loop playsinline>
                                <source src="<?php echo esc_url($col3_video_url); ?>" type="video/mp4">
                                <?php _e('Your browser does not support the video tag.', 'rewindrecords'); ?>
                            </video>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</section>
