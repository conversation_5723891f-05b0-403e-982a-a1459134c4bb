$(document).ready(function () {
    $(document).on("initPage", function () {
        if ($(".randomRecordsSlider").length > 0) {
            initRandomSliders();
        }
    });
});

function initRandomSliders() {
    // Find all random records sliders on the page
    const sliders = document.querySelectorAll('.recordsSlider');
    // Initialize Flickity for each slider
    sliders.forEach(slider => {
        if (!slider.classList.contains('flickity-enabled')) {
            var flickitySlider = new Flickity(slider, {
                cellAlign: 'left',
                contain: true,
                wrapAround: true,
                autoPlay: 5000,
                prevNextButtons: true,
                pageDots: false,
                adaptiveHeight: false,
                imagesLoaded: true,
                percentPosition: true,
                resize: true,
                // Responsive settings
                responsive: [
                    {
                        breakpoint: 1160,
                        settings: {
                            groupCells: 3
                        }
                    },
                    {
                        breakpoint: 768,
                        settings: {
                            groupCells: 2
                        }
                    },
                    {
                        breakpoint: 480,
                        settings: {
                            groupCells: 1
                        }
                    }
                ]
            });
            
            // Add pointer-events handling during drag
            $(slider).on('dragStart.flickity', function(event) {
                $(slider).find("a").css("pointer-events", "none");
            });

            $(slider).on('dragEnd.flickity', function(event) {
                $(slider).find("a").css("pointer-events", "inherit");
            });
        }
    });
    
    // Re-initialize Flickity when the page is loaded via AJAX (for Swup)
    if (window.swup) {
        swup.on('contentReplaced', function() {
            const newSliders = document.querySelectorAll('.random-records-slider .recordsSlider');
            
            newSliders.forEach(slider => {
                if (!slider.classList.contains('flickity-enabled')) {
                    var flickitySlider = new Flickity(slider, {
                        cellAlign: 'left',
                        contain: true,
                        wrapAround: true,
                        autoPlay: 5000,
                        prevNextButtons: true,
                        pageDots: false,
                        adaptiveHeight: false,
                        imagesLoaded: true,
                        percentPosition: true,
                        resize: true,
                        // Responsive settings
                        responsive: [
                            {
                                breakpoint: 1160,
                                settings: {
                                    groupCells: 3
                                }
                            },
                            {
                                breakpoint: 768,
                                settings: {
                                    groupCells: 2
                                }
                            },
                            {
                                breakpoint: 480,
                                settings: {
                                    groupCells: 1
                                }
                            }
                        ]
                    });
                    
                    // Add pointer-events handling during drag
                    $(slider).on('dragStart.flickity', function(event) {
                        $(slider).find("a").css("pointer-events", "none");
                    });

                    $(slider).on('dragEnd.flickity', function(event) {
                        $(slider).find("a").css("pointer-events", "inherit");
                    });
                }
            });
        });
    }
}
