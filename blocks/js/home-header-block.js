var autoSlide;
$(document).ready(function () {
  $(document).on("initPage", function () {
      if (autoSlide) autoSlide.kill();
      if ($(".homeHeaderBlock").length > 0){
        initializeHomeHeaderBlock();
      }

      if ($("body.touch").length > 0) {
        $(document).on("touchend", ".homeHeaderBlock .button", function(event) {
            event.preventDefault();
            let link = $(this).attr("href");
            if (link && typeof pageContainerWrap !== "undefined") {
              pageContainerWrap.loadPage({ url: link });
            } else {
            }
        });
    }
  });
});


function initializeHomeHeaderBlock() {
  var currentIndex = 0;
  var slides = $(".homeHeaderBlock .projectSlider .slide");
  var totalSlides = slides.length;
  var direction = "next";

  var heighestTitleSlide = 0;

  $(".homeHeaderBlock .projectSlider .titleSlide").each(function (index, slide) {
    var titleHeight = $(slide).outerHeight();
    if (titleHeight > heighestTitleSlide) {
      heighestTitleSlide = titleHeight;
    }
  });

  $(".homeHeaderBlock .projectSlider .titleSlider").css("height", heighestTitleSlide);

  function startAutoSlide() {
    if (autoSlide) autoSlide.kill();
    autoSlide = gsap.timeline({ repeat: -1 });
    autoSlide.to({}, { duration: 5, onComplete: function () {
      nextSlide(currentIndex, totalSlides);
    }});
  }

  function nextSlide() {
    direction = "next";
    currentIndex = (currentIndex + 1) % totalSlides;
    showSlide(currentIndex, false);
    startAutoSlide();
  }


  function showSlide(index, first=false) {
    updateActiveTitleSlide(index, first);
  }


  function updateActiveTitleSlide(index, first) {
    if (index == totalSlides -1) {
      index = 0;
    } else if(first) {
      index = 0;
    } else {
      index = index + 1;
    }

    $(".homeHeaderBlock .projectSlider .slide").removeClass("active");
    $(".homeHeaderBlock .projectSlider .slide").eq(index).addClass("active");
    $(".homeHeaderBlock .projectSlider .titleSlide").eq(index).addClass("active");

    $(".homeHeaderBlock .projectSlider .titleSlide").removeClass("active");
    $(".homeHeaderBlock .projectSlider .titleSlide").eq(index).addClass("active");
  }

  $(".homeHeaderBlock .next").click(function () {
    nextSlide(currentIndex, totalSlides);
  });

  $(".homeHeaderBlock .prev").click(function () {
    direction = "prev";
    currentIndex = (currentIndex - 1 + totalSlides) % totalSlides;
    showSlide(currentIndex, false);
    startAutoSlide();
  });

  showSlide(currentIndex, true);
  startAutoSlide();
}

