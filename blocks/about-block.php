<?php
/**
 * Block Name: About Block
 * Description: A block with a rotating vinyl record on the left and text content on the right
 */

// Get block values
$title = get_field('title');
$content = get_field('content');
$links = get_field('links');
$image = get_field('image');
$vinyl_image = get_field('vinyl_image');
$background_color = get_field('background_color') ?: '#1A1A1A';

// Block ID
$block_id = 'about-block-' . $block['id'];

// Block classes
$classes = 'aboutBlock';

if (!empty($block['className'])) {
    $classes .= ' ' . $block['className'];
}

if (!empty($block['align'])) {
    $classes .= ' align' . $block['align'];
}
?>

<section id="<?php echo esc_attr($block_id); ?>" class="<?php echo esc_attr($classes); ?>" data-init <?php if (get_field("anchor")): ?>data-anchor="<?php the_field("anchor") ?>"<?php endif; ?> >
    <div class="contentWrapper">
        <div class="cols">
            <!-- Column 1: Vinyl Record -->
            <div class="col vinylCol">
                <div class="vinylWrapper">
                    <div class="vinyl" id="vinyl-<?php echo esc_attr($block['id']); ?>">
                        <?php if ($vinyl_image) : ?>
                            <img class="lazy" data-src="<?php echo esc_url($vinyl_image['sizes']['medium_large']); ?>" alt="<?php echo esc_attr($vinyl_image['alt']); ?>" />
                        <?php else : ?>
                            <div class="defaultVinyl">
                                <svg viewBox="0 0 500 500" xmlns="http://www.w3.org/2000/svg">
                                    <circle cx="250" cy="250" r="250" fill="#333333"/>
                                    <circle cx="250" cy="250" r="100" fill="#F5D042"/>
                                    <circle cx="250" cy="250" r="20" fill="#333333"/>
                                    <circle cx="250" cy="250" r="5" fill="#FFFFFF"/>
                                </svg>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            
            <!-- Column 2: Title, Text, Links, and Image -->
            <div class="col textCol">
                <div class="innerContent">
                    <?php if ($title) : ?>
                        <h2 class="hugeTitle" data-lines data-words><?php echo esc_html($title); ?></h2>
                    <?php endif; ?>
                    
                    <?php if ($content) : ?>
                        <div class="text" data-lines data-words><?php echo $content; ?></div>
                    <?php endif; ?>
                    
                    <?php if ($links) : ?>
                        <div class="links">
                            <?php foreach ($links as $link_item) : ?>
                                <?php 
                                $link = $link_item['link'];
                                if ($link) : 
                                ?>
                                    <a href="<?php echo esc_url($link['url']); ?>" class="textLink" target="<?php echo esc_attr($link['target']); ?>" title="<?php echo esc_attr($link['title']); ?>">
                                        <span class="innerText"><?php echo esc_html($link['title']); ?></span>
                                        <span class="arrows">
                                            <i class="icon-arrow-right-up"></i>
                                            <i class="icon-arrow-right-up"></i>
                                        </span>
                                    </a>
                                <?php endif; ?>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                    
                    <?php if ($image) : ?>
                        <div class="additionalImageWrapper">
                            <div class="additionalImage">
                                <img class="lazy" data-src="<?php echo esc_url($image['sizes']['medium_large']); ?>" alt="<?php echo esc_attr($image['alt']); ?>" />
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</section>
