// out: false
@import '../../assets/less/vw_values.less';
@import '../../assets/less/constants.less';

.homeHeaderBlock {
  &.inview {
    .cols {
      .col {
        .imageWrapper {
          .innerImage {
              img, video {
                .transform(scale(1));
              }
            }
          }
        }
    }
  }
  .cols {
    display: block;
    .titleWrapper {
      position: relative;
      .textLink {
        position: absolute;
        right: @vw100;
        top: @vw70;
        transition-delay: 1.2s;
      }
    }

    .col {
      display: inline-block;
      width: 50%;
      vertical-align: middle;

      @media (max-width: 580px) {
        display: block;
        width: 100%;
        margin-bottom: @vw60-580;

        &:last-child {
          margin-bottom: 0;
        }
      }

      .imageWrapper {
        width: 100%;
        overflow: hidden;
        .transform(translate3d(0,0,0));
        .innerImage {
          .paddingRatio(1,1);
        }
        img,
        video {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          display: block;
          object-fit: cover;
          object-position: center;
          .transform(scale(1.2));
          .transitionMore(transform , .9s, .45s, cubic-bezier(0.83, 0, 0.17, 1));
          will-change: transform;
        }
      }
    }

    .text {
      margin: @vw40 0 @vw60 0;
      padding-left: @vw118;
      padding-right: @vw100;
      p {
        font-style: italic;
      }
    }
    .links {
      padding-left: @vw118;
    }
  }

  // Media position variations
  &.media-left {
    .cols {
      direction: ltr;
    }
  }

  &.media-right {
    .cols {
      direction: rtl;

      .col {
        direction: ltr;
      }
    }
  }
}

// Responsive styles
@media all and (max-width: 1160px) {
  .homeHeaderBlock {
    padding: @vw50-1160 0;
    .bigTitle {
      padding-right: @vw50-580;
    }
    .text {
        margin: @vw40-1160 0 @vw60-1160 0;
        padding-left: @vw118-1160;
        padding-right: @vw100-1160;
      }
      .links {
        padding-left: @vw118-1160;
      }
      .cols {
        .titleWrapper {
          .textLink {
            right: @vw80-1160;
            top: @vw30-1160;
          }
        }
      }
  }
}
@media all and (max-width: 580px) {
  .homeHeaderBlock {
    .cols {
      .text {
        margin: @vw40-580 0 @vw60-580 0;
        padding-left: 0;
        padding-right: 0;
      }
      .links {
        padding-left: 0;
      }
      .titleWrapper {
        display: flex;
        align-items: start;
        gap: @vw40-580;
        flex-wrap: wrap;
        flex-direction: column-reverse;
        .textLink {
          top: 0;
          position: relative;
          right: auto;
          left: 0;
        }
      }
    }
  }
}
