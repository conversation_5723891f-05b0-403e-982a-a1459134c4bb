// out: false
@import '../../assets/less/vw_values.less';
@import '../../assets/less/constants.less';

.recordOfTheWeekBlock {
  padding: @vw150 0;
  color: @hardBlack;
  &.inview {
    .col {
      .vinyl {
        right: -@vw90;
        .transitionMore(right, .45s, .6s, cubic-bezier(0.34, 1.56, 0.64, 1));
        img {
          .transform(rotate(0));
          .transitionMore(transform, .75s, .45s, cubic-bezier(0.34, 1.56, 0.64, 1));
        }
      }
    }
  }
  .rotw-container {
    max-width: @vw1000;
    margin: 0 auto;
  }

  .rotw-title {
    font-family: @headingFont;
    font-size: @vw32;
    font-weight: 400;
    letter-spacing: 2px;
    color: @almostWhite;
    margin-bottom: @vw30;
    text-align: center;
    position: relative;

    &:after {
      content: '';
      display: block;
      width: @vw60;
      height: 2px;
      background: @secondaryColor;
      margin: @vw10 auto 0;
    }
  }

  .col {
    width: 50%;
    display: inline-block;
    vertical-align: middle;
    &.imageCol {
      padding-right: @vw118 + @vw16;
    }
    &.textCol {
      padding-left: @vw16;
    }
  }
  .imageWrapper {
    width: (@vw118 * 3) + (@vw16 * 2);
    display: block;
    height: auto;
    position: relative;
    img {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      object-fit: cover;
      object-position: center;
    }
    .innerImage {
      height: 0;
      padding-bottom: 100%;
      width: 100%;
    }
    .vinyl {
      position: absolute;
      top: 50%;
      left: auto;
      width: 100%;
      height: 100%;
      object-fit: cover;
      object-position: center;
      right: 0;
      .transform(translateY(-50%));
      img {
        object-fit: contain;
        transform-origin: center;
        .transform(rotate(60deg));
      }
    }
    .defaultCover {
      width: 100%;
      padding-bottom: 100%;
      position: relative;
      background: @vinylColor;
      border-radius: @vw10;
      box-shadow: 0 @vw10 @vw30 rgba(0, 0, 0, 0.4);
    }
    .rotw-info {
      flex: 2;
      min-width: @vw300;

      .rotw-record-title {
        font-family: @headingFont;
        font-size: @vw28;
        font-weight: 400;
        letter-spacing: 1px;
        color: @almostWhite;
        margin: 0 0 @vw10;

        a {
          color: @almostWhite;
          text-decoration: none;
          transition: color 0.3s ease;

          &:hover {
            color: @secondaryColor;
          }
        }
      }

      .rotw-artist {
        font-family: @bodyFont;
        font-size: @vw20;
        color: @secondaryColor;
        margin-bottom: @vw20;
      }

      .rotw-description {
        font-family: @bodyFont;
        font-size: @vw16;
        line-height: 1.6;
        color: @grey;
        margin-bottom: @vw30;

        p {
          margin-bottom: @vw15;

          &:last-child {
            margin-bottom: 0;
          }
        }
      }

      .rotw-link {
        .button {
          display: inline-block;
          padding: @vw12 @vw30;
          background: @secondaryColor;
          color: @hardWhite;
          font-family: @headingFont;
          font-size: @vw18;
          font-weight: 400;
          letter-spacing: 1px;
          text-decoration: none;
          border-radius: @vw5;
          transition: background 0.3s ease;
          &:hover {
            background: darken(@secondaryColor, 10%);
          }
        }
      }
    }
  }
  .signature {
    display: block;
    width: 100%;
    margin-top: @vw10;
    text-align: right;
  }
  svg {
    position: relative;
    width: @vw118;
    height: auto;
    display: inline-block;
    path {
      fill: #CE8F35;
      stroke: #CE8F35;
    }
  }
}

// Responsive styles
@media all and (max-width: 1160px) {
  .recordOfTheWeekBlock {
    padding: @vw150-1160 0;

    .rotw-container {
      max-width: @vw1000-1160;
    }

    .rotw-title {
      font-size: @vw32-1160;
      margin-bottom: @vw30-1160;

      &:after {
        width: @vw60-1160;
        margin: @vw10-1160 auto 0;
      }
    }

    .col {
      &.imageCol {
        padding-right: @vw118-1160 + @vw16-1160;
      }

      &.textCol {
        padding-left: @vw16-1160;
      }
    }

    .imageWrapper {
      width: (@vw118-1160 * 3) + (@vw16-1160 * 2);

      .defaultCover {
        border-radius: @vw10-1160;
        box-shadow: 0 @vw10-1160 @vw30-1160 rgba(0, 0, 0, 0.4);
      }

      .vinyl {
        &.inview {
          right: -@vw90-1160;
        }
      }

      .rotw-info {
        min-width: @vw300-1160;

        .rotw-record-title {
          font-size: @vw28-1160;
          margin: 0 0 @vw10-1160;
        }

        .rotw-artist {
          font-size: @vw20-1160;
          margin-bottom: @vw20-1160;
        }

        .rotw-description {
          font-size: @vw16-1160;
          margin-bottom: @vw30-1160;

          p {
            margin-bottom: @vw15-1160;
          }
        }

        .rotw-link {
          .button {
            padding: @vw12-1160 @vw30-1160;
            font-size: @vw18-1160;
            border-radius: @vw5-1160;
          }
        }
      }
    }

    .signature {
      margin-top: @vw10-1160;

      svg {
        width: @vw118-1160;
      }
    }
  }
}

@media all and (max-width: 580px) {
  .recordOfTheWeekBlock {
    padding: @vw100-580 0;

    .rotw-container {
      max-width: 100%;
    }

    .rotw-title {
      font-size: @vw32-580;
      margin-bottom: @vw30-580;

      &:after {
        width: @vw60-580;
        margin: @vw10-580 auto 0;
      }
    }

    .col {
      display: block;
      width: 100%;
      margin-bottom: @vw30-580;

      &:last-child {
        margin-bottom: 0;
      }

      &.imageCol {
        padding-right: 0;
        max-width: 80%;
        margin: 0 auto;
      }

      &.textCol {
        padding-left: 0;
        text-align: center;
      }
    }

    .imageWrapper {
      width: 100%;

      .defaultCover {
        border-radius: @vw10-580;
        box-shadow: 0 @vw10-580 @vw30-580 rgba(0, 0, 0, 0.4);
      }

      .vinyl {
        display: none;
      }

      .rotw-info {
        min-width: 100%;

        .rotw-record-title {
          font-size: @vw28-580;
          margin: 0 0 @vw10-580;
        }

        .rotw-artist {
          font-size: @vw20-580;
          margin-bottom: @vw20-580;
        }

        .rotw-description {
          font-size: @vw16-580;
          margin-bottom: @vw30-580;

          p {
            margin-bottom: @vw15-580;
          }
        }

        .rotw-link {
          .button {
            padding: @vw12-580 @vw30-580;
            font-size: @vw18-580;
            border-radius: @vw5-580;
          }
        }
      }
    }

    .signature {
      margin-top: @vw10-580;
      text-align: center;

      svg {
        width: @vw118-580;
      }
    }
  }
}
