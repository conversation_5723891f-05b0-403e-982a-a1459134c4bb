// out: false
@import '../../assets/less/vw_values.less';
@import '../../assets/less/constants.less';
@import '../../assets/less/partials/record-item.less';

.randomRecordsSlider {
  .sliderWrapper {
    padding-left: @vw118 + @vw16;
  }
  .col {
    display: inline-block;
    vertical-align: middle;
    width: 50%;
    &.right {
      text-align: right;
    }
  }
  .normalTitle {
      padding-left: @vw118;
  }
  .recordsSlider {
    margin: 0 -@vw15;
    margin-top: @vw55;
    white-space: nowrap;
    .flickity-viewport {
      overflow: visible;
    }
  }

  // Flickity customization
  .flickity-button {
    background-color: transparent;
    color: @almostWhite;
    border: 1px solid @almostWhite;
    border-radius: 50%;
    width: @vw120;
    height: @vw120;
    font-size: @vw36;
    position: absolute;
    top: 50%;
    .transform(translateY(-50%));
    transition: background 0.3s ease, color 0.3s ease, border-color 0.3s ease;
    -webkit-transition: background 0.3s ease, color 0.3s ease, border-color 0.3s ease;
    &:hover {
      background: @almostWhite;
      color: @hardBlack;
      border-color: @almostWhite;
    }

    &:focus {
      outline: none;
    }

    &.previous {
      left: @vw10;
    }

    &.next {
      right: @vw10;
    }
  }

  .flickity-button-icon {
    fill: currentColor;
  }
}

// Responsive styles
@media all and (max-width: 1160px) {
  .randomRecordsSlider {
    .sliderWrapper {
      padding-left: 0;
    }
    .normalTitle {
        padding-left: 0;
    }

    .recordsSlider {
      margin: 0 -@vw15-1160;
      margin-top: @vw55-1160;
    }

    .flickity-button {
      width: @vw120-1160;
      height: @vw120-1160;
      font-size: @vw22-1160;

      &.previous {
        left: @vw10-1160;
      }

      &.next {
        right: @vw10-1160;
      }
    }
  }
}

@media all and (max-width: 580px) {
  .randomRecordsSlider {
    .normalTitle {
      text-align: center;
    }

    .col {
      display: block;
      width: 100%;
      margin-bottom: @vw50-580;

      &.right {
        text-align: center;
      }
    }

    .recordsSlider {
      margin: 0 -@vw15-580;
      margin-top: @vw30-580;
    }

    .flickity-button {
      width: @vw100-580;
      height: @vw100-580;
      font-size: @vw20-580;

      &.previous {
        left: @vw10-580;
      }

      &.next {
        right: @vw10-580;
      }
    }
  }
}
