// out: false
.smallTextMarqueeBlock {
    background: @grey;
    padding: @vw5 0 0 0;
    .marquee {
        white-space: nowrap;
        .itemsContainer {
            display: inline-block;
            .item {
                display: inline-block;
                position: relative;
                width: auto;
                color: @hardBlack;
            }
        }
    }
}

@media (max-width: 1160px) {
    .smallTextMarqueeBlock {
      padding: @vw5-1160 0 0 0;
      .marquee {
        .itemsContainer {
          .item {
            margin: 0 @vw6-1160;
          }
        }
      }
    }
  }
  
  @media (max-width: 580px) {
    .smallTextMarqueeBlock {
      padding: @vw5-580 0 0 0;
      .marquee {
        .itemsContainer {
          .item {
            margin: 0 @vw6-580;
          }
        }
      }
    }
  }
  