// out: ../css/contact-block.css, compress: true, strictMath: true
@import '../../assets/less/vw_values.less';
@import '../../assets/less/constants.less';

.contactBlock {
  color: @hardWhite;
  .col {
      display: inline-block;
      position: relative;
      vertical-align: middle;
      margin-bottom: @vw40;
      width: 25%;
      .images {
        margin-left: -@vw8;
        width: calc(100% ~"+" @vw16);
        .imageWrapper {
          display: inline-block;
          margin: 0 @vw8;
          height: auto;
          width: calc(50% ~"-" @vw16);
          vertical-align: top;
          a {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            text-decoration: none;
            color: @hardWhite;
            cursor: pointer;
          }
          .innerImage {
            height: (@vw100 * 3) + @vw80;
            position: relative;
            overflow: hidden;
            video, img {
              position: absolute;
              top: 0;
              left: 0;
              width: 100%;
              height: 100%;
              object-fit: cover;
              object-position: center;
            }
          }
        }
      }
      &:nth-child(1) {
        .imageWrapper {
          &:last-child {
            margin-top: @vw100 + @vw30;
            -webkit-mask-image: linear-gradient(90deg, #000,rgba(0, 0, 0, 0));
            mask-image: linear-gradient(90deg, #000,rgba(0, 0, 0, 0));
          }
        }
      }
      &:nth-child(2) {
        width: 50%;
        .formWrapper {
          padding: @vw100 @vw112 + @vw16 @vw50 @vw112 + @vw16;
          .rounded(@vw20);
          border: 2px dashed @hardWhite;
          .formIntro {
            text-align: center;
          }
          .field {
            display: block;
            &:not(:last-of-type) {
              margin-bottom: @vw20;
            }
            &.half {
              vertical-align: top;
              display: inline-block;
              width: 50%;
              padding-right: @vw20;
            }
            &.submit {
              input {
                background-color: @primaryColor;
                border: 2px solid @primaryColor;
                cursor: pointer;
                padding: @vw22 @vw22;
                .transition(.3s);
                -webkit-box-shadow: 0 0 @vw20 transparent;
                box-shadow: 0 0 @vw20 transparent;
                color: @hardWhite;
                text-align: center !important;
                &:hover {
                  transform: scale(.95);
                  -webkit-box-shadow: 0 @vw10 @vw50 @primaryColor;
                  box-shadow: 0 @vw10 @vw50 @primaryColor;
                }
              }
              .textLink {
                &:hover {
                  input {
                    padding-left: @vw30;
                    padding-right: 0;
                  }
                }
              }
            }
            label {
              display: block;
              font-size: @vw30;
              margin-bottom: @vw10;
            }
            input, textarea {
              background-color: @almostWhite;
              font-family: @headingFont;
              font-size: @vw30;
              color: @hardBlack;
              font-weight: 400;
              line-height: 1.2;
              width: 100%;
              display: block;
              border: none;
              position: relative;
              overflow: hidden;
              text-overflow: ellipsis;
              padding: @vw15 @vw20;
              .rounded(@vw5);
              .transition(.3s);
              &:focus {
                box-shadow: 0 0 0 2px @primaryColor;
              }
            }
            textarea {
              resize: none;
              min-height: @vw150;
            }
            .wpcf7-not-valid-tip {
              margin-top: @vw5;
              font-family: @headingFont;
              font-size: @vw22;
            }
          }
        }
      }
      &:nth-child(3) {
        .imageWrapper {
          &:first-child {
            margin-top: @vw100 + @vw30;
            -webkit-mask-image: linear-gradient(-90deg, #000,rgba(0, 0, 0, 0));
            mask-image: linear-gradient(-90deg, #000,rgba(0, 0, 0, 0));
          }
        }
      }
  }

  /* Contact Form 7 Specific Styling */
  .wpcf7 {
    width: 100%;

    form {
      width: 100%;

      p {
        margin-bottom: @vw20;
        position: relative;

        label {
          display: block;
          font-family: @headingFont;
          font-size: @vw30;
          margin-bottom: @vw10;
          letter-spacing: 1px;
          text-transform: uppercase;
        }

        .wpcf7-form-control-wrap {
          display: block;
          width: 100%;

          input[type="text"],
          input[type="email"],
          input[type="tel"],
          textarea {
            background-color: @almostWhite;
            font-family: @headingFont;
            font-size: @vw30;
            color: @hardBlack;
            font-weight: 400;
            line-height: 1.2;
            width: 100%;
            display: block;
            border: none;
            position: relative;
            overflow: hidden;
            text-overflow: ellipsis;
            padding: @vw15 @vw20;
            .rounded(@vw5);
            .transition(.3s);

            &:focus {
              box-shadow: 0 0 0 2px @primaryColor;
            }

            &::placeholder {
              color: @darkerGrey;
              opacity: 0.7;
            }
          }

          textarea {
            resize: none;
            min-height: @vw150;
          }
        }

        .wpcf7-submit {
          background-color: @primaryColor;
          border: 2px solid @primaryColor;
          cursor: pointer;
          padding: @vw22 @vw22;
          .transition(.3s);
          -webkit-box-shadow: 0 0 @vw20 transparent;
          box-shadow: 0 0 @vw20 transparent;
          color: @hardWhite;
          text-align: center !important;
          font-family: @headingFont;
          font-size: @vw30;
          letter-spacing: 1px;
          text-transform: uppercase;
          min-width: @vw150;
          .rounded(@vw5);
          display: block;
          width: 100%;
          &:hover {
            transform: scale(.95);
            -webkit-box-shadow: 0 @vw10 @vw50 @primaryColor;
            box-shadow: 0 @vw10 @vw50 @primaryColor;
          }
        }
      }
    }

    .wpcf7-spinner {
      position: absolute;
      right: -@vw40;
      top: 50%;
      transform: translateY(-50%);
      margin: 0;
      background-color: @primaryColor;
      opacity: 0.7;
    }
  }

  .wpcf7-response-output,
  .wpcf7-not-valid-tip,
  .wpcf7 form.invalid .wpcf7-response-output,
  .wpcf7 form.unaccepted .wpcf7-response-output,
  .wpcf7 form.payment-required .wpcf7-response-output {
    background: rgba(255,0,0,.2);
    color: @primaryColor;
    .rounded(@vw5);
    padding: @vw10 @vw15;
    font-size: @vw14;
    border: 1px solid @primaryColor;
    font-family: @headingFont;
    margin-top: @vw20;
    text-align: center;
  }

  .wpcf7 form.sent .wpcf7-response-output {
    background: rgba(0,255,0,.2);
    color: @secondaryColor;
    .rounded(@vw5);
    padding: @vw10 @vw15;
    font-size: @vw14;
    border: 1px solid @secondaryColor;
    font-family: @headingFont;
  }

  .wpcf7-not-valid-tip {
    background: transparent;
    border: none;
    padding: @vw5 0;
    color: @primaryColor;
    font-size: @vw12;
    margin-top: @vw5;
  }
}

@media all and (max-width: 1160px) {
  .contactBlock {
    .col {
      margin-bottom: @vw40-1160;
      .images {
        margin-left: -@vw8-1160;
        width: calc(100% + @vw16-1160);
        .imageWrapper {
          margin: 0 @vw8-1160;
          width: calc(50% - @vw16-1160);
          .innerImage {
            height: (@vw100-1160 * 3) + @vw80-1160;
          }
        }
      }
      &:nth-child(1) .imageWrapper:last-child {
        margin-top: @vw100-1160 + @vw30-1160;
      }
      &:nth-child(2) {
        .formWrapper {
          padding: @vw100-1160 @vw40-1160 @vw50-1160 @vw40-1160;
          .field {
            &:not(:last-of-type) {
              margin-bottom: @vw20-1160;
            }
            &.half {
              padding-right: @vw20-1160;
            }
            &.submit input {
              padding: @vw22-1160 @vw22-1160;
              -webkit-box-shadow: 0 0 @vw20-1160 transparent;
              box-shadow: 0 0 @vw20-1160 transparent;
              &:hover {
                -webkit-box-shadow: 0 @vw10-1160 @vw50-1160 @primaryColor;
                box-shadow: 0 @vw10-1160 @vw50-1160 @primaryColor;
              }
            }
            label {
              font-size: @vw30-1160;
              margin-bottom: @vw10-1160;
            }
            input, textarea {
              font-size: @vw30-1160;
              line-height: @vw22-1160;
              padding: @vw15-1160 @vw20-1160;
              .rounded(@vw5-1160);
            }
            .wpcf7-not-valid-tip {
              margin-top: @vw5-1160;
              font-size: @vw12-1160;
            }
            textarea {
              min-height: @vw150-1160;
            }
          }
        }
      }
      &:nth-child(3) .imageWrapper:first-child {
        margin-top: @vw100-1160 + @vw30-1160;
      }
    }

    /* Contact Form 7 Specific Styling - 1160px */
    .wpcf7 {
      form {
        p {
          margin-bottom: @vw20-1160;

          label {
            font-size: @vw30-1160;
            margin-bottom: @vw10-1160;
          }

          .wpcf7-form-control-wrap {
            input[type="text"],
            input[type="email"],
            input[type="tel"],
            textarea {
              font-size: @vw30-1160;
              line-height: @vw22-1160;
              padding: @vw15-1160 @vw20-1160;
              .rounded(@vw5-1160);
            }

            textarea {
              min-height: @vw150-1160;
            }
          }

          .wpcf7-submit {
            padding: @vw22-1160 @vw22-1160;
            font-size: @vw30-1160;
            min-width: @vw150-1160;
            .rounded(@vw5-1160);

            &:hover {
              -webkit-box-shadow: 0 @vw10-1160 @vw50-1160 @primaryColor;
              box-shadow: 0 @vw10-1160 @vw50-1160 @primaryColor;
            }
          }
        }
      }

      .wpcf7-spinner {
        right: -@vw40-1160;
      }
    }

    .wpcf7-response-output,
    .wpcf7-not-valid-tip,
    .wpcf7 form.invalid .wpcf7-response-output,
    .wpcf7 form.unaccepted .wpcf7-response-output,
    .wpcf7 form.payment-required .wpcf7-response-output {
      .rounded(@vw5-1160);
      padding: @vw10-1160 @vw15-1160;
      font-size: @vw14-1160;
      margin-top: @vw20-1160;
    }

    .wpcf7 form.sent .wpcf7-response-output {
      .rounded(@vw5-1160);
      padding: @vw10-1160 @vw15-1160;
      font-size: @vw14-1160;
    }

    .wpcf7-not-valid-tip {
      padding: @vw5-1160 0;
      font-size: @vw12-1160;
      margin-top: @vw5-1160;
    }
  }
}

@media all and (max-width: 580px) {
  .contactBlock {
    .cols {
      display: block; /* Ensure proper display on mobile */
    }
    .col {
      margin-bottom: @vw40-580;
      width: 100% !important; /* Force full width for all columns */
      display: block;

      /* Hide the image columns on mobile */
      &:nth-child(1), &:nth-child(3) {
        display: none;
      }

      /* Show only the form column */
      &:nth-child(2) {
        width: 100% !important;
        display: block;

        .formWrapper {
          padding: @vw50-580 @vw22-580;
          width: 100%;

          .formIntro {
            margin-bottom: @vw30-580;
          }

          .contactFormWrapper {
            display: block;
            width: 100%;
          }

          .field {
            width: 100%;
            &:not(:last-of-type) {
              margin-bottom: @vw20-580;
            }
            &.half {
              width: 100%;
              padding-right: @vw20-580;
            }
            &.submit input {
              padding: @vw22-580 @vw22-580;
              -webkit-box-shadow: 0 0 @vw20-580 transparent;
              box-shadow: 0 0 @vw20-580 transparent;
              &:hover {
                -webkit-box-shadow: 0 @vw10-580 @vw50-580 @primaryColor;
                box-shadow: 0 @vw10-580 @vw50-580 @primaryColor;
              }
            }
            label {
              display: none;
            }
            input, textarea {
              font-size: @vw30-580;
              line-height: 1.2;
              padding: @vw15-580 @vw20-580;
              .rounded(@vw5-580);
            }
            .wpcf7-not-valid-tip {
              margin-top: @vw5-580;
              font-size: @vw16-580;
            }
            textarea {
              min-height: @vw200-580;
            }
          }
        }
      }
    }

    /* Contact Form 7 Specific Styling - 580px */
    .wpcf7 {
      display: block !important;
      width: 100% !important;

      form {
        display: block !important;
        width: 100% !important;

        p {
          margin-bottom: @vw30-580;
          display: block !important;
          width: 100% !important;

          label {
            font-size: @vw30-580;
            margin-bottom: @vw10-580;
          }

          .wpcf7-form-control-wrap {
            display: block !important;
            width: 100% !important;

            input[type="text"],
            input[type="email"],
            input[type="tel"],
            textarea {
              font-size: @vw30-580;
              line-height: 1.2;
              padding: @vw15-580 @vw20-580;
              .rounded(@vw5-580);
              display: block !important;
              width: 100% !important;

              &::placeholder {
                opacity: 1;
                color: @darkerGrey;
              }
            }

            textarea {
              min-height: @vw200-580;
            }
          }

          .wpcf7-submit {
            padding: @vw22-580 @vw22-580;
            font-size: @vw30-580;
            min-width: @vw200-580;
            .rounded(@vw5-580);
            width: 100%;
            display: block !important;

            &:hover {
              -webkit-box-shadow: 0 @vw10-580 @vw50-580 @primaryColor;
              box-shadow: 0 @vw10-580 @vw50-580 @primaryColor;
            }
          }
        }
      }

      .wpcf7-spinner {
        position: absolute;
        right: auto;
        left: 50%;
        top: auto;
        bottom: -@vw40-580;
        transform: translateX(-50%);
      }
    }

    .wpcf7-response-output,
    .wpcf7-not-valid-tip,
    .wpcf7 form.invalid .wpcf7-response-output,
    .wpcf7 form.unaccepted .wpcf7-response-output,
    .wpcf7 form.payment-required .wpcf7-response-output {
      .rounded(@vw5-580);
      padding: @vw15-580;
      font-size: @vw16-580;
      margin-top: @vw40-580;
      text-align: center;
    }

    .wpcf7 form.sent .wpcf7-response-output {
      .rounded(@vw5-580);
      padding: @vw15-580;
      font-size: @vw16-580;
    }

    .wpcf7-not-valid-tip {
      padding: @vw5-580 0;
      font-size: @vw16-580;
      margin-top: @vw5-580;
    }
  }
}
