// out: false
@import '../../assets/less/vw_values.less';
@import '../../assets/less/constants.less';

.spotifyTextBlock {
  padding: @vw50 0;

  .contentWrapper {
    .flexbox();
    margin: 0 -@vw20;

    @media (max-width: 768px) {
      margin: 0;
    }
  }

  .col {
    .inline-block();
    width: calc(50% - @vw40);
    margin: 0 @vw20;

    &.textCol {
      padding: 0 @vw20;
    }

    @media (max-width: 768px) {
      display: block;
      width: 100%;
      margin: 0 0 @vw40 0;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }

  .spotifyWrapper {
    height: 100%;
    min-height: 352px;
    width: 100%;
    position: relative;
    overflow: hidden;

    iframe {
      border-radius: @vw10;
      box-shadow: 0 @vw10 @vw30 rgba(0, 0, 0, 0.4);
    }
  }

  .normalTitle {
    margin-bottom: @vw20;
  }

  .bigTitle {
    font-family: @headingFont;
    font-size: @vw32;
    font-weight: 400;
    letter-spacing: 2px;
    color: @almostWhite;
    margin-bottom: @vw20;
    line-height: 1.1;
  }

  .text {
    font-family: @bodyFont;
    font-size: @vw16;
    line-height: 1.6;
    color: @grey;
    margin-bottom: @vw30;

    p {
      margin-bottom: @vw15;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}

// Responsive styles
@media all and (max-width: 1160px) {
  .spotifyTextBlock {
    padding: @vw50-1160 0;

    .contentWrapper {
      gap: @vw30-1160;
    }

    .col {
      &.textCol {
        padding: 0 @vw20-1160;
      }
    }

    .spotifyWrapper {
      min-height: 300px;

      iframe {
        border-radius: @vw10-1160;
        box-shadow: 0 @vw10-1160 @vw30-1160 rgba(0, 0, 0, 0.4);
      }
    }

    .normalTitle {
      margin-bottom: @vw20-1160;
    }

    .bigTitle {
      font-size: @vw32-1160;
      margin-bottom: @vw20-1160;
    }

    .text {
      font-size: @vw16-1160;
      margin-bottom: @vw30-1160;

      p {
        margin-bottom: @vw15-1160;
      }
    }
  }
}

@media all and (max-width: 580px) {
  .spotifyTextBlock {
    padding: @vw50-580 0;

    .contentWrapper {
      gap: @vw30-580;
    }

    .col {
      &.textCol {
        padding: 0 @vw20-580;
      }
    }

    .spotifyWrapper {
      min-height: 250px;

      iframe {
        border-radius: @vw10-580;
        box-shadow: 0 @vw10-580 @vw30-580 rgba(0, 0, 0, 0.4);
      }
    }

    .normalTitle {
      margin-bottom: @vw20-580;
    }

    .bigTitle {
      font-size: @vw28-580;
      margin-bottom: @vw20-580;
    }

    .text {
      font-size: @vw16-580;
      margin-bottom: @vw30-580;

      p {
        margin-bottom: @vw15-580;
      }
    }
  }
}
