// out: false
@import '../../assets/less/vw_values.less';
@import '../../assets/less/constants.less';

.imagesTextBlock {
  &.inview {
    .popupImageWrapper {
      .transform(scale(1)translate(50%,-50%));
      .transitionMore(transform, .6s, .45s, cubic-bezier(0.34, 1.56, 0.64, 1));
    }
  }
  .cols {
    height: (@vw100 * 8) + @vw13;
    margin-left: -@vw8;
    width: calc(100% ~"+" @vw16);
    .col {
      .inline-block();
      width: calc(33.3333% ~"-" @vw16);
      height: 100%;
      margin: 0 @vw8;
      &:last-child {
        z-index: -1;
      }
      &.textCol {
        display: inline-flex;
        flex-wrap: wrap;
        flex-direction: row;
        gap: @vw16;
        .textColInner {
          display: block;
          height: 100%;
          position: relative;

          > * {
            margin-bottom: @vw16;

            &:last-child {
              margin-bottom: 0;
            }
          }
        }
      }

      &.mediaCol {
        .mediaWrapper {
          height: 100%;
          position: relative;
          overflow: hidden;
          .innerImage {
            width: 100%;
            overflow: hidden;
            height: 100%;
            position: absolute;
            img,
            video {
              position: absolute;
              top: 0;
              left: 0;
              height: 100%;
              width: 100%;
              object-fit: cover;
            }
          }
        }
      }
    }
  }
  .innerContent {
    position: relative;
    text-align: center;
    padding: @vw50;
    border: 2px dashed @hardWhite;
    .rounded(@vw20);
  }
  .popupImageWrapper {
    width: @vw190;
    height: @vw190;
    display: block;
    background: @hardWhite;
    position: absolute;
    top: 0;
    right: 0;
    transform-origin: center;
    .transform(scale(0)translate(50%,-50%));
    .rounded(50%);
    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      position: absolute;
      top: 0;
      left: 0;
      mix-blend-mode: hard-light;
    }
  }

  .text {
    color: @grey;
    margin-bottom: @vw30;

    p {
      margin-bottom: @vw15;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }

  .links {
    margin-top: @vw20;
    margin-bottom: @vw30;
  }
  .innerContent, .middleImageWrapper {
    height: calc(50% ~"-" @vw8);
  }
  .middleImageWrapper {
    width: 100%;
    .middleImage {
      width: 100%;
      position: absolute;
      top: 0;
      left: 0;
      object-fit: cover;
      height: 100%;
      display: block;
      border-radius: @vw5;
    }
  }
}

// Responsive styles
@media all and (max-width: 1160px) {
  .imagesTextBlock {
    .cols {
      height: (@vw100-1160 * 8) + @vw13-1160;
      margin-left: -@vw8-1160;
      width: calc(100% ~"+" @vw16-1160);

      .col {
        width: calc(33.3333% ~"-" @vw16-1160);
        margin: 0 @vw8-1160;

        &.textCol {
          gap: @vw16-1160;

          .textColInner {
            > * {
              margin-bottom: @vw16-1160;
            }
          }
        }
      }
    }

    .innerContent {
      padding: @vw50-1160;
      border-radius: @vw20-1160;
    }

    .popupImageWrapper {
      width: @vw190-1160;
      height: @vw190-1160;
      margin-bottom: @vw20-1160;

      .popupImage {
        border-radius: @vw5-1160;
      }
    }

    .text {
      margin-bottom: @vw30-1160;

      p {
        margin-bottom: @vw15-1160;
      }
    }

    .links {
      margin-top: @vw20-1160;
      margin-bottom: @vw30-1160;

      a.textLink {
        margin-bottom: @vw15-1160;

        i {
          margin-right: @vw10-1160;
        }

        .divider {
          margin-top: @vw5-1160;
        }

        &:hover {
          i {
            transform: translateX(@vw5-1160);
          }
        }
      }
    }

    .innerContent, .middleImageWrapper {
      height: calc(50% ~"-" @vw8-1160);
    }

    .middleImageWrapper {
      .middleImage {
        border-radius: @vw5-1160;
      }
    }
  }
}

@media all and (max-width: 580px) {
  .imagesTextBlock {
    &.inview {
      .popupImageWrapper {
        .transform(scale(1)translate(0,-40%));
        .transitionMore(transform, .6s, .45s, cubic-bezier(0.34, 1.56, 0.64, 1));
      }
    }
    .cols {
      height: auto;
      margin: 0;
      width: 100%;
      .col {
        display: block;
        width: 100% !important;
        height: auto;
        margin-bottom: @vw60-580;
        &:last-child {
          margin-bottom: 0;
        }
        &.textCol {
          display: block;
          .textColInner {
            > * {
              margin-bottom: @vw16-580;
            }
          }
        }

        &.mediaCol {
          .mediaWrapper {
            height: 0;
            padding-bottom: 75%;

            .innerImage {
              position: absolute;
              height: 100%;
            }
          }
        }
      }
    }

    img, video {
      .transform(translate3d(0,0,0)) !important;
    }
    .innerContent {
      padding: @vw50-580;
      border-radius: @vw20-580;
    }

    .popupImageWrapper {
      width: @vw110-580;
      height: @vw110-580;
      margin-bottom: @vw20-580;
      .popupImage {
        border-radius: @vw5-580;
      }
    }

    .text {
      margin-bottom: @vw30-580;
      p {
        margin-bottom: @vw15-580;
      }
    }

    .links {
      margin-top: @vw30-580;
      margin-bottom: 0;
    }

    .innerContent, .middleImageWrapper {
      height: auto;
    }

    .middleImageWrapper {
      display: none;
    }
  }
}
