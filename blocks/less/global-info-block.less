// out: false
@import '../../assets/less/vw_values.less';
@import '../../assets/less/constants.less';

.globalInfoBlock {
  .cols {
    width: 100%;

    &:after {
      content: "";
      display: table;
      clear: both;
    }

    .col {
      display: inline-block;
      vertical-align: top;
      width: 33.3333%;
      &.imageCol {
        width: 66.6666%;
      }
    }
    .infoContent {
      margin: @vw20 0 @vw40 0;
    }

    .imageCol {
      padding-left: @vw40;

      .mediaWrapper {
        position: relative;
        width: 100%;
        height: 0;
        .paddingRatio(977, 620);
        .imageWrapper {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;

          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
        }

        .mapWrapper {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;

          .blackMap {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: @almostBlack;
            img {
              position: absolute;
              top: 0;
              left: 0;
              width: 100%;
              height: 100%;
              object-fit: cover;
              object-position: center;
            }
          }

          .mapMarker {
            position: absolute;
            top: 33%;
            left: 54%;
            transform: translate(-50%, -50%);
            z-index: 2;

            .markerPin {
              position: relative;
              text-align: center;
              font-size: @vw32;
              transform-origin: center;
              height: @vw10;
              width: @vw10;
              .transform(rotate(45deg));
              background: @hardWhite;
            }
            .markerPopup {
              position: absolute;
              bottom: -@vw5;
              left: 50%;
              transform: translateX(-50%);
              width: @vw120;
              height: @vw120;
              margin-bottom: @vw10;
              border-radius: 50%;
              overflow: hidden;
              border: @vw5 solid @hardWhite;
              background-color: @hardWhite;
              img {
                width: 100%;
                height: 100%;
                object-fit: cover;
              }

              &:after {
                content: '';
                position: absolute;
                bottom: -@vw10;
                left: 50%;
                transform: translateX(-50%);
                width: 0;
                height: 0;
                border-left: @vw10 solid transparent;
                border-right: @vw10 solid transparent;
                border-top: @vw10 solid @secondaryColor;
              }
            }
          }
        }
      }
    }

    .contentCol {
      padding: @vw100 @vw50;
      border: 2px dashed @hardWhite;
      .rounded(@vw20);
    }
  }
}

// Responsive styles
@media all and (max-width: 1160px) {
  .globalInfoBlock {
    .cols {
      .col {
        width: 40%;
        &.imageCol {
          width: 60%;
        }
      }

      .infoContent {
        margin: @vw20-1160 0 @vw40-1160 0;
      }

      .imageCol {
        padding-left: @vw40-1160;

        .mediaWrapper {
          .mapWrapper {
            .mapMarker {
              .markerPin {
                font-size: @vw32-1160;
                height: @vw10-1160;
                width: @vw10-1160;
              }

              .markerPopup {
                bottom: -@vw5-1160;
                width: @vw120-1160;
                height: @vw120-1160;
                margin-bottom: @vw10-1160;
                border: @vw5-1160 solid @hardWhite;
                &:after {
                  bottom: -@vw10-1160;
                  border-left: @vw10-1160 solid transparent;
                  border-right: @vw10-1160 solid transparent;
                  border-top: @vw10-1160 solid @secondaryColor;
                }
              }
            }
          }
        }
      }

      .contentCol {
        padding: @vw100-1160 @vw50-1160;
        .rounded(@vw20-1160);
      }
    }
  }
}

@media all and (max-width: 580px) {
  .globalInfoBlock {
    .cols {
      .col {
        display: block;
        width: 100%;
        margin-bottom: @vw40-580;

        &:last-child {
          margin-bottom: 0;
        }

        &.imageCol {
          width: 100%;
          .transform(translate3d(0,0,0)) !important;
        }
      }
      .infoContent {
        margin: @vw20-580 0 @vw40-580 0;
      }

      .imageCol {
        padding-left: 0;

        .mediaWrapper {
          .mapWrapper {
            .mapMarker {
              .markerPin {
                font-size: @vw32-580;
                height: @vw10-580;
                width: @vw10-580;
              }

              .markerPopup {
                bottom: -@vw5-580;
                width: @vw120-580;
                height: @vw120-580;
                margin-bottom: @vw10-580;
                border: @vw5-580 solid @hardWhite;
                &:after {
                  bottom: -@vw10-580;
                  border-left: @vw10-580 solid transparent;
                  border-right: @vw10-580 solid transparent;
                  border-top: @vw10-580 solid @secondaryColor;
                }
              }
            }
          }
        }
      }

      .contentCol {
        padding: @vw50-580 @vw30-580;
        .rounded(@vw20-580);
      }
    }
  }
}
