// out: false
@import '../../assets/less/vw_values.less';
@import '../../assets/less/constants.less';

.rewind-home-header-block {
  padding: @vw50 0;
  background: @almostBlack;

  .rewind-home-header-content {
    .flexbox();
    margin: 0 -@vw20;

    @media (max-width: 768px) {
      margin: 0;
    }

    .rewind-home-header-left {
      .inline-block();
      width: 50%;
      padding: 0 @vw20;
      min-width: @vw300;

      @media (max-width: 768px) {
        display: block;
        width: 100%;
        margin-bottom: @vw40;
      }
    }

    .rewind-home-header-right {
      .inline-block();
      width: 50%;
      padding: 0 @vw20;
      min-width: @vw300;
      max-width: @vw600;

      @media (max-width: 768px) {
        display: block;
        width: 100%;
      }
    }

    .rewind-home-header-info {
      .rewind-home-header-title {
        font-family: @headingFont;
        font-size: @vw50;
        font-weight: 400;
        letter-spacing: 2px;
        color: @almostWhite;
        margin-bottom: @vw20;
        line-height: 1.1;
      }

      .rewind-home-header-location {
        display: block;
        margin-bottom: @vw20;
        font-family: @bodyFont;
        font-size: @vw16;
        color: @secondaryColor;

        i {
          display: inline-block;
          vertical-align: middle;
          margin-right: @vw10;
          font-size: @vw20;
          color: @secondaryColor;
        }

        a {
          color: @secondaryColor;
          text-decoration: none;
          transition: color 0.3s ease;

          &:hover {
            text-decoration: underline;
          }
        }
      }

      .rewind-home-header-text {
        font-family: @bodyFont;
        font-size: @vw16;
        line-height: 1.6;
        color: @grey;
        margin-bottom: @vw30;

        p {
          margin-bottom: @vw15;

          &:last-child {
            margin-bottom: 0;
          }
        }
      }

      .rewind-home-header-links {
        display: block;

        .rewind-home-header-link {
          display: block;
          margin-bottom: @vw15;
          font-family: @headingFont;
          font-size: @vw18;
          font-weight: 400;
          letter-spacing: 1px;
          color: @almostWhite;
          text-decoration: none;
          transition: color 0.3s ease;

          &:last-child {
            margin-bottom: 0;
          }

          .link-text {
            display: inline-block;
            vertical-align: middle;
            margin-right: @vw10;
          }

          i {
            display: inline-block;
            vertical-align: middle;
            font-size: @vw16;
            transition: transform 0.3s ease;
          }

          &:hover {
            color: @secondaryColor;

            i {
              transform: translateX(@vw5);
            }
          }
        }
      }
    }

    .rewind-home-header-media {
      .rewind-home-header-image,
      .rewind-home-header-video {
        width: 100%;
        border-radius: @vw10;
        overflow: hidden;
        box-shadow: 0 @vw10 @vw30 rgba(0, 0, 0, 0.4);

        img,
        video {
          width: 100%;
          height: auto;
          display: block;
          object-fit: cover;
        }
      }
    }
  }
}

// Responsive styles
@media all and (max-width: 1160px) {
  .rewind-home-header-block {
    padding: @vw50-1160 0;

    .rewind-home-header-content {
      margin: 0 -@vw15-1160;

      .rewind-home-header-left,
      .rewind-home-header-right {
        padding: 0 @vw15-1160;
        min-width: @vw250-1160;
      }

      .rewind-home-header-right {
        max-width: @vw500-1160;
      }

      .rewind-home-header-info {
        .rewind-home-header-title {
          font-size: @vw50-1160;
          margin-bottom: @vw20-1160;
        }

        .rewind-home-header-location {
          margin-bottom: @vw20-1160;
          font-size: @vw16-1160;

          i {
            margin-right: @vw10-1160;
            font-size: @vw20-1160;
          }
        }

        .rewind-home-header-text {
          font-size: @vw16-1160;
          margin-bottom: @vw30-1160;

          p {
            margin-bottom: @vw15-1160;
          }
        }

        .rewind-home-header-links {
          .rewind-home-header-link {
            margin-bottom: @vw15-1160;
            font-size: @vw18-1160;

            .link-text {
              margin-right: @vw10-1160;
            }

            i {
              font-size: @vw16-1160;
            }

            &:hover {
              i {
                transform: translateX(@vw5-1160);
              }
            }
          }
        }
      }

      .rewind-home-header-media {
        .rewind-home-header-image,
        .rewind-home-header-video {
          border-radius: @vw10-1160;
          box-shadow: 0 @vw10-1160 @vw30-1160 rgba(0, 0, 0, 0.4);
        }
      }
    }
  }
}

@media all and (max-width: 580px) {
  .rewind-home-header-block {
    padding: @vw50-580 0;

    .rewind-home-header-content {
      margin: 0;

      .rewind-home-header-left,
      .rewind-home-header-right {
        display: block;
        width: 100%;
        padding: 0;
        min-width: 100%;
      }

      .rewind-home-header-right {
        max-width: 100%;
      }

      .rewind-home-header-info {
        .rewind-home-header-title {
          font-size: @vw40-580;
          margin-bottom: @vw20-580;
        }

        .rewind-home-header-location {
          margin-bottom: @vw20-580;
          font-size: @vw16-580;

          i {
            margin-right: @vw10-580;
            font-size: @vw20-580;
          }
        }

        .rewind-home-header-text {
          font-size: @vw16-580;
          margin-bottom: @vw30-580;

          p {
            margin-bottom: @vw15-580;
          }
        }

        .rewind-home-header-links {
          .rewind-home-header-link {
            margin-bottom: @vw15-580;
            font-size: @vw18-580;

            .link-text {
              margin-right: @vw10-580;
            }

            i {
              font-size: @vw16-580;
            }

            &:hover {
              i {
                transform: translateX(@vw5-580);
              }
            }
          }
        }
      }

      .rewind-home-header-media {
        .rewind-home-header-image,
        .rewind-home-header-video {
          border-radius: @vw10-580;
          box-shadow: 0 @vw10-580 @vw30-580 rgba(0, 0, 0, 0.4);
        }
      }
    }
  }
}
