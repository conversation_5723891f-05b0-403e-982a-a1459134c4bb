<?php
/**
 * Block Name: Latest Records
 * Description: Display the most recent records from your collection
 */

// Get block values
$title = get_field('title');
$records_count = get_field('records_count') ?: 8;
$show_artist = get_field('show_artist');
$show_year = get_field('show_year');
$show_format = get_field('show_format');
$link = get_field('link');

// Get latest records based on release date
$latest_records = new WP_Query(array(
    'post_type' => 'record',
    'posts_per_page' => $records_count,
    'meta_key' => '_record_release_date',
    'orderby' => 'meta_value',
    'order' => 'DESC',
    'meta_query' => array(
        array(
            'key' => '_record_release_date',
            'compare' => 'EXISTS',
        ),
    ),
    'tax_query' => array(
        'relation' => 'AND',
        // Only show albums
        array(
            'taxonomy' => 'record_product_type',
            'field' => 'slug',
            'terms' => 'album',
        ),
        // Exclude records with format 'Spellen' or other non-music formats
        array(
            'taxonomy' => 'record_format',
            'field' => 'slug',
            'terms' => array('spellen', 'bordspellen', 'games', 'board-games'),
            'operator' => 'NOT IN',
        ),
    ),
));

// Block ID
$block_id = 'latestRecordsSlider-' . $block['id'];

// Block classes
$classes = 'latestRecordsSlider';
if (!empty($block['className'])) {
    $classes .= ' ' . $block['className'];
}
if (!empty($block['align'])) {
    $classes .= ' align' . $block['align'];
}
?>

<section id="<?php echo esc_attr($block_id); ?>" class="<?php echo esc_attr($classes); ?>" data-init>
    <div class="contentWrapper">
        <div class="col">
            <?php if ($title) : ?>
                <h2 class="normalTitle" data-lines data-words><?php echo esc_html($title); ?></h2>
            <?php endif; ?>
        </div>
        <div class="col right">
            <a href="<?php echo esc_url($link['url']); ?>" title="<?php echo esc_html($link['title']);  ?>" class="textLink" <?php echo $link['target'] ? 'target="_blank" rel="noopener noreferrer"' : ''; ?>>
                <i class="icon-arrow-right"></i>
                <span class="innerMask">
                    <span class="innerWrapper">
                        <span class="innerText absolute"><?php echo esc_html($link['title']); ?></span>
                        <span class="innerText absolute" aria-hidden="true"><?php echo esc_html($link['title']); ?></span>
                    </span>
                </span>
                <span class="divider">
                    <svg xmlns="http://www.w3.org/2000/svg" width="184.5" height="2" viewBox="0 0 184.5 2">
                        <line data-name="Line 6" x2="184.5" transform="translate(0 1)" fill="none" stroke="#eee" stroke-width="2"/>
                    </svg>
                </span>
            </a>
        </div>
        <div class="sliderWrapper">
            <?php if ($latest_records->have_posts()) : ?>
                <div class="recordsSlider">
                    <?php
                    while ($latest_records->have_posts()) : $latest_records->the_post();
                        // Wrap the record item in a slide div
                        echo '<div class="recordSlide">';
                        rewindrecords_render_record_item(get_the_ID(), array(
                            'image_size' => 'record-cover-optimized',
                            'show_artist' => $show_artist,
                            'show_year' => $show_year,
                            'show_format' => true,
                            'show_price' => true,
                            'lazy_load' => true,
                        ));
                        echo '</div>';
                    endwhile;
                    ?>
                </div>
            <?php else : ?>
                <div class="noRecords">
                    <p><?php _e('No records found.', 'rewindrecords'); ?></p>
                </div>
            <?php endif; ?>
            <?php wp_reset_postdata(); ?>
        </div>
    </div>
</section>
