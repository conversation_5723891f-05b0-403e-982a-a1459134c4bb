<?php
/**
 * Records Delete
 *
 * Handles deleting records and their associated media
 */

class Rewindrecords_Records_Delete {

    /**
     * Constructor
     */
    public function __construct() {
        // Add admin menu
        add_action('admin_menu', array($this, 'add_admin_menu'));
        
        // Add AJAX handlers
        add_action('wp_ajax_rewindrecords_delete_all_records', array($this, 'ajax_delete_all_records'));
    }

    /**
     * Add admin menu
     */
    public function add_admin_menu() {
        add_submenu_page(
            'edit.php?post_type=record',
            __('Delete All Records', 'rewindrecords'),
            __('Delete All Records', 'rewindrecords'),
            'manage_options',
            'rewindrecords-delete-records',
            array($this, 'render_admin_page')
        );
    }

    /**
     * Render admin page
     */
    public function render_admin_page() {
        // Get record count
        $record_count = wp_count_posts('record');
        $published_count = $record_count->publish;
        $draft_count = $record_count->draft;
        $total_count = $published_count + $draft_count;
        
        // Get media count (attachments associated with records)
        $media_count = $this->count_record_attachments();
        
        ?>
        <div class="wrap">
            <h1><?php _e('Delete All Records', 'rewindrecords'); ?></h1>
            
            <div class="notice notice-warning">
                <p><strong><?php _e('Warning: This action cannot be undone!', 'rewindrecords'); ?></strong></p>
                <p><?php _e('This will permanently delete all records and their associated media files.', 'rewindrecords'); ?></p>
            </div>
            
            <div class="card">
                <h2><?php _e('Records Summary', 'rewindrecords'); ?></h2>
                <p><?php printf(__('Total Records: %d', 'rewindrecords'), $total_count); ?></p>
                <p><?php printf(__('Published Records: %d', 'rewindrecords'), $published_count); ?></p>
                <p><?php printf(__('Draft Records: %d', 'rewindrecords'), $draft_count); ?></p>
                <p><?php printf(__('Associated Media Files: %d', 'rewindrecords'), $media_count); ?></p>
            </div>
            
            <div class="delete-records-form">
                <h2><?php _e('Delete All Records', 'rewindrecords'); ?></h2>
                
                <?php if ($total_count > 0): ?>
                    <p><?php _e('Type "DELETE" in the field below to confirm deletion:', 'rewindrecords'); ?></p>
                    <input type="text" id="delete-confirmation" class="regular-text" placeholder="DELETE">
                    
                    <p class="submit">
                        <button id="delete-records-button" class="button button-primary button-large" disabled>
                            <?php _e('Delete All Records and Media', 'rewindrecords'); ?>
                        </button>
                        <span class="spinner"></span>
                    </p>
                    
                    <div id="delete-progress" style="display: none;">
                        <div class="progress-bar">
                            <div class="progress-bar-fill" style="width: 0%;"></div>
                        </div>
                        <p class="progress-status"></p>
                    </div>
                    
                    <div id="delete-results" style="display: none;"></div>
                    
                    <script>
                    jQuery(document).ready(function($) {
                        // Enable button only when confirmation text is correct
                        $('#delete-confirmation').on('input', function() {
                            var confirmText = $(this).val();
                            $('#delete-records-button').prop('disabled', confirmText !== 'DELETE');
                        });
                        
                        // Handle delete button click
                        $('#delete-records-button').on('click', function(e) {
                            e.preventDefault();
                            
                            if (!confirm('<?php _e('Are you absolutely sure you want to delete ALL records and their media? This cannot be undone!', 'rewindrecords'); ?>')) {
                                return;
                            }
                            
                            // Show spinner and progress
                            $('.spinner').addClass('is-active');
                            $('#delete-progress').show();
                            
                            // Disable button
                            $(this).prop('disabled', true);
                            
                            // Start deletion
                            deleteAllRecords();
                        });
                        
                        function deleteAllRecords() {
                            $.ajax({
                                url: ajaxurl,
                                type: 'POST',
                                data: {
                                    action: 'rewindrecords_delete_all_records',
                                    nonce: '<?php echo wp_create_nonce('rewindrecords_delete_records'); ?>'
                                },
                                success: function(response) {
                                    $('.spinner').removeClass('is-active');
                                    
                                    if (response.success) {
                                        // Update progress
                                        $('.progress-bar-fill').css('width', '100%');
                                        $('.progress-status').text(response.data.message);
                                        
                                        // Show results
                                        $('#delete-results').html('<div class="notice notice-success"><p>' + response.data.message + '</p></div>').show();
                                        
                                        // Refresh counts after a delay
                                        setTimeout(function() {
                                            window.location.reload();
                                        }, 3000);
                                    } else {
                                        $('#delete-results').html('<div class="notice notice-error"><p>' + response.data + '</p></div>').show();
                                    }
                                },
                                error: function() {
                                    $('.spinner').removeClass('is-active');
                                    $('#delete-results').html('<div class="notice notice-error"><p><?php _e('An error occurred during deletion.', 'rewindrecords'); ?></p></div>').show();
                                }
                            });
                        }
                    });
                    </script>
                <?php else: ?>
                    <p><?php _e('There are no records to delete.', 'rewindrecords'); ?></p>
                <?php endif; ?>
            </div>
        </div>
        
        <style>
        .progress-bar {
            height: 20px;
            background-color: #f0f0f0;
            border-radius: 3px;
            margin-bottom: 10px;
            overflow: hidden;
        }
        .progress-bar-fill {
            height: 100%;
            background-color: #0073aa;
            transition: width 0.3s ease;
        }
        .spinner {
            float: none;
            margin-left: 10px;
            vertical-align: middle;
        }
        .delete-records-form {
            max-width: 600px;
            margin-top: 20px;
        }
        </style>
        <?php
    }

    /**
     * Count attachments associated with records
     * 
     * @return int Number of attachments
     */
    private function count_record_attachments() {
        global $wpdb;
        
        // Get all record IDs
        $record_ids = get_posts(array(
            'post_type' => 'record',
            'posts_per_page' => -1,
            'fields' => 'ids',
        ));
        
        if (empty($record_ids)) {
            return 0;
        }
        
        // Count attachments that are featured images of records
        $count = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(DISTINCT meta.meta_value) 
            FROM $wpdb->postmeta meta 
            WHERE meta.meta_key = '_thumbnail_id' 
            AND meta.post_id IN (" . implode(',', array_fill(0, count($record_ids), '%d')) . ")",
            $record_ids
        ));
        
        return (int) $count;
    }

    /**
     * Delete all records and their associated media
     * 
     * @return array Results of deletion
     */
    public function delete_all_records() {
        $results = array(
            'records_deleted' => 0,
            'media_deleted' => 0,
            'errors' => array(),
        );
        
        // Get all record IDs
        $record_ids = get_posts(array(
            'post_type' => 'record',
            'posts_per_page' => -1,
            'fields' => 'ids',
        ));
        
        if (empty($record_ids)) {
            return $results;
        }
        
        // First, collect all attachment IDs associated with records
        $attachment_ids = $this->get_record_attachment_ids($record_ids);
        
        // Delete each record
        foreach ($record_ids as $record_id) {
            $deleted = wp_delete_post($record_id, true); // true = force delete, bypass trash
            
            if ($deleted) {
                $results['records_deleted']++;
            } else {
                $results['errors'][] = sprintf(__('Failed to delete record ID: %d', 'rewindrecords'), $record_id);
            }
        }
        
        // Delete all collected attachments
        foreach ($attachment_ids as $attachment_id) {
            $deleted = wp_delete_attachment($attachment_id, true); // true = force delete, bypass trash
            
            if ($deleted) {
                $results['media_deleted']++;
            } else {
                $results['errors'][] = sprintf(__('Failed to delete attachment ID: %d', 'rewindrecords'), $attachment_id);
            }
        }
        
        return $results;
    }

    /**
     * Get all attachment IDs associated with records
     * 
     * @param array $record_ids Array of record post IDs
     * @return array Array of attachment IDs
     */
    private function get_record_attachment_ids($record_ids) {
        global $wpdb;
        
        if (empty($record_ids)) {
            return array();
        }
        
        // Get featured image IDs
        $attachment_ids = $wpdb->get_col($wpdb->prepare(
            "SELECT DISTINCT meta.meta_value 
            FROM $wpdb->postmeta meta 
            WHERE meta.meta_key = '_thumbnail_id' 
            AND meta.post_id IN (" . implode(',', array_fill(0, count($record_ids), '%d')) . ")",
            $record_ids
        ));
        
        return array_map('intval', $attachment_ids);
    }

    /**
     * AJAX handler for deleting all records
     */
    public function ajax_delete_all_records() {
        // Verify nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'rewindrecords_delete_records')) {
            wp_send_json_error(__('Security check failed', 'rewindrecords'));
        }
        
        // Check user capabilities
        if (!current_user_can('manage_options')) {
            wp_send_json_error(__('You do not have permission to perform this action', 'rewindrecords'));
        }
        
        // Delete all records and their media
        $results = $this->delete_all_records();
        
        // Format response message
        $message = sprintf(
            __('Successfully deleted %d records and %d associated media files.', 'rewindrecords'),
            $results['records_deleted'],
            $results['media_deleted']
        );
        
        if (!empty($results['errors'])) {
            $message .= ' ' . sprintf(
                __('There were %d errors during deletion.', 'rewindrecords'),
                count($results['errors'])
            );
        }
        
        wp_send_json_success(array(
            'message' => $message,
            'records_deleted' => $results['records_deleted'],
            'media_deleted' => $results['media_deleted'],
            'errors' => $results['errors'],
        ));
    }
}
