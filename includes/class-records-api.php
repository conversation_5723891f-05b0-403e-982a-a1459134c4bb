<?php
/**
 * Records API Integration
 */

class Rewindrecords_Records_API {

    /**
     * API base URL
     */
    private $api_url = 'https://api.discogs.com';

    /**
     * User token for authentication
     */
    private $user_token;

    /**
     * User agent for API requests
     */
    private $user_agent = 'RewindrecordsApp/1.0 +https://rewindrecords.com';

    /**
     * Cache for API responses
     */
    private $api_cache = array();

    /**
     * Constructor
     */
    public function __construct() {
        $this->user_token = get_option('rewindrecords_api_token', '');
    }

    /**
     * Set the user token
     */
    public function set_token($token) {
        $this->user_token = $token;
        update_option('rewindrecords_api_token', $token);
    }

    /**
     * Get the user token
     */
    public function get_token() {
        return $this->user_token;
    }

    /**
     * Make a request to the API
     */
    public function make_request($endpoint, $params = array()) {
        // Check if token is set
        if (empty($this->user_token)) {
            return new WP_Error('no_token', __('No API token set', 'rewindrecords'));
        }

        // Build URL with parameters
        $url = $this->api_url . $endpoint;
        if (!empty($params)) {
            $url = add_query_arg($params, $url);
        }

        // Add token to URL
        $url = add_query_arg('token', $this->user_token, $url);

        // Generate cache key
        $cache_key = md5($url);

        // Check if we have a cached response
        if (isset($this->api_cache[$cache_key])) {
            return $this->api_cache[$cache_key];
        }

        // Set up request arguments
        $args = array(
            'headers' => array(
                'User-Agent' => $this->user_agent,
            ),
            'timeout' => 15,
        );

        // Make the request
        $response = wp_remote_get($url, $args);

        // Check for errors
        if (is_wp_error($response)) {
            return $response;
        }

        // Check response code
        $response_code = wp_remote_retrieve_response_code($response);
        if ($response_code !== 200) {
            return new WP_Error(
                'api_error',
                sprintf(__('API error: %s', 'rewindrecords'), $response_code)
            );
        }

        // Parse response
        $body = wp_remote_retrieve_body($response);
        $data = json_decode($body, true);

        if (json_last_error() !== JSON_ERROR_NONE) {
            return new WP_Error('json_error', __('Error parsing API response', 'rewindrecords'));
        }

        // Cache the response
        $this->api_cache[$cache_key] = $data;

        return $data;
    }

    /**
     * Get user identity
     */
    public function get_identity() {
        return $this->make_request('/oauth/identity');
    }

    /**
     * Get user collection
     */
    public function get_collection($username, $page = 1, $per_page = 50) {
        $params = array(
            'page' => $page,
            'per_page' => $per_page,
        );

        return $this->make_request('/users/' . $username . '/collection/folders/0/releases', $params);
    }

    /**
     * Get release details
     */
    public function get_release($release_id) {
        return $this->make_request('/releases/' . $release_id);
    }

    /**
     * Import collection to WordPress
     */
    public function import_collection($username) {
        // Get user identity to verify token
        $identity = $this->get_identity();
        if (is_wp_error($identity)) {
            return $identity;
        }

        // Start with page 1
        $page = 1;
        $per_page = 50;
        $total_imported = 0;
        $total_updated = 0;
        $errors = array();

        // Keep track of imported record IDs to prevent duplicates
        $imported_record_ids = array();

        // Get all existing record IDs from the database
        $existing_records = $this->get_existing_record_ids();

        do {
            // Get collection page
            $collection = $this->get_collection($username, $page, $per_page);

            if (is_wp_error($collection)) {
                $errors[] = $collection->get_error_message();
                break;
            }

            // Process releases
            if (!empty($collection['releases'])) {
                foreach ($collection['releases'] as $release) {
                    // Skip if we've already processed this record in this import session
                    if (in_array($release['id'], $imported_record_ids)) {
                        continue;
                    }

                    // Add to our tracking array
                    $imported_record_ids[] = $release['id'];

                    // Check if this record already exists
                    $is_update = isset($existing_records[$release['id']]);

                    // Import or update the record
                    $result = $this->import_release($release, $existing_records);

                    if (is_wp_error($result)) {
                        $errors[] = $result->get_error_message();
                    } else {
                        if ($is_update) {
                            $total_updated++;
                        } else {
                            $total_imported++;
                        }
                    }
                }
            }

            // Move to next page
            $page++;

            // Continue until we've processed all pages
        } while ($collection['pagination']['page'] < $collection['pagination']['pages']);

        return array(
            'total_imported' => $total_imported,
            'total_updated' => $total_updated,
            'errors' => $errors,
        );
    }

    /**
     * Get all existing record IDs from the database
     */
    private function get_existing_record_ids() {
        static $cached_records = null;

        // Return cached results if available
        if ($cached_records !== null) {
            return $cached_records;
        }

        global $wpdb;

        $records = array();

        // Query to get all record IDs and their post IDs
        $results = $wpdb->get_results(
            "SELECT post_id, meta_value
            FROM {$wpdb->postmeta}
            WHERE meta_key = '_record_id'",
            ARRAY_A
        );

        if ($results) {
            foreach ($results as $row) {
                $records[$row['meta_value']] = $row['post_id'];
            }
        }

        // Cache the results
        $cached_records = $records;

        return $records;
    }

    /**
     * Import a single release
     *
     * @param array $release The release data from the API
     * @param array $existing_records Optional. Array of existing record IDs mapped to post IDs
     * @return int|WP_Error The post ID on success, WP_Error on failure
     */
    private function import_release($release, $existing_records = null) {
        // Get existing record ID from database if not provided
        if ($existing_records === null) {
            $existing_records = $this->get_existing_record_ids();
        }

        $existing_post_id = isset($existing_records[$release['id']]) ? $existing_records[$release['id']] : null;

        // Get full release details
        $release_details = $this->get_release($release['id']);
        if (is_wp_error($release_details)) {
            return $release_details;
        }

        // Prepare post data
        $post_data = array(
            'post_title' => $release_details['title'],
            'post_content' => !empty($release_details['notes']) ? $release_details['notes'] : '',
            'post_status' => 'publish',
            'post_type' => 'record',
        );

        // Update existing or create new
        if ($existing_post_id) {
            $post_data['ID'] = $existing_post_id;
            $post_id = wp_update_post($post_data);
        } else {
            $post_id = wp_insert_post($post_data);
        }

        if (is_wp_error($post_id)) {
            return $post_id;
        }

        // Save release metadata
        update_post_meta($post_id, '_record_id', $release_details['id']);
        update_post_meta($post_id, '_record_year', $release_details['year']);
        update_post_meta($post_id, '_record_country', $release_details['country']);

        // Save suggested price
        $suggested_price = $this->calculate_suggested_price($release_details, $release);
        update_post_meta($post_id, '_record_suggested_price', $suggested_price);

        // Save formats
        if (!empty($release_details['formats'])) {
            $format_names = array();
            foreach ($release_details['formats'] as $format) {
                $format_names[] = $format['name'];

                // Add descriptions as separate formats
                if (!empty($format['descriptions'])) {
                    foreach ($format['descriptions'] as $description) {
                        $format_names[] = $description;
                    }
                }
            }

            // Set format terms
            if (!empty($format_names)) {
                wp_set_object_terms($post_id, $format_names, 'record_format');
            }
        }

        // Save genres and styles
        $genres = array();
        if (!empty($release_details['genres'])) {
            $genres = array_merge($genres, $release_details['genres']);
        }
        if (!empty($release_details['styles'])) {
            $genres = array_merge($genres, $release_details['styles']);
        }

        if (!empty($genres)) {
            wp_set_object_terms($post_id, $genres, 'record_genre');
        }

        // Save artists
        if (!empty($release_details['artists'])) {
            $artist_names = array();
            foreach ($release_details['artists'] as $artist) {
                $artist_names[] = $artist['name'];
            }

            if (!empty($artist_names)) {
                wp_set_object_terms($post_id, $artist_names, 'record_artist');
            }
        }

        // Save tracklist
        if (!empty($release_details['tracklist'])) {
            update_post_meta($post_id, '_record_tracklist', $release_details['tracklist']);
        }

        // Save cover image
        if (!empty($release_details['images'])) {
            foreach ($release_details['images'] as $image) {
                if ($image['type'] === 'primary') {
                    // Download and attach image
                    $this->set_featured_image_from_url($post_id, $image['uri']);
                    break;
                }
            }
        }

        return $post_id;
    }

    /**
     * Calculate suggested price for a record
     */
    private function calculate_suggested_price($release_details, $release) {
        $suggested_price = 0;

        // Check if there's a price in the release data
        if (!empty($release['basic_information']['lowest_price'])) {
            $suggested_price = $release['basic_information']['lowest_price'];
        } elseif (!empty($release_details['lowest_price'])) {
            $suggested_price = $release_details['lowest_price'];
        }

        // If no price is available, calculate based on various factors
        if ($suggested_price <= 0) {
            // Base price
            $suggested_price = 15.0; // Default base price

            // Adjust based on format
            if (!empty($release_details['formats'])) {
                foreach ($release_details['formats'] as $format) {
                    if (strtolower($format['name']) === 'vinyl') {
                        $suggested_price += 5.0;

                        // Check for special formats
                        if (!empty($format['descriptions'])) {
                            foreach ($format['descriptions'] as $desc) {
                                $desc = strtolower($desc);
                                if ($desc === 'limited edition') {
                                    $suggested_price += 10.0;
                                } elseif ($desc === 'colored vinyl' || $desc === 'picture disc') {
                                    $suggested_price += 8.0;
                                } elseif ($desc === '180 gram' || $desc === '200 gram') {
                                    $suggested_price += 5.0;
                                }
                            }
                        }
                    } elseif (strtolower($format['name']) === 'cd') {
                        $suggested_price += 0; // No additional price for CD
                    } elseif (strtolower($format['name']) === 'cassette') {
                        $suggested_price -= 5.0; // Lower price for cassette
                    }
                }
            }

            // Adjust based on year
            if (!empty($release_details['year'])) {
                $year = intval($release_details['year']);
                $current_year = intval(date('Y'));

                if ($year < 1970) {
                    $suggested_price += 15.0; // Older records are more valuable
                } elseif ($year < 1990) {
                    $suggested_price += 8.0;
                } elseif ($year < 2000) {
                    $suggested_price += 5.0;
                } elseif ($year > ($current_year - 2)) {
                    $suggested_price += 3.0; // New releases
                }
            }

            // Adjust based on condition (if available)
            if (!empty($release['condition']) || !empty($release['basic_information']['condition'])) {
                $condition = !empty($release['condition']) ? $release['condition'] : $release['basic_information']['condition'];

                switch (strtolower($condition)) {
                    case 'mint':
                    case 'm':
                        $suggested_price *= 1.5;
                        break;
                    case 'near mint':
                    case 'nm':
                    case 'nm+':
                        $suggested_price *= 1.3;
                        break;
                    case 'very good plus':
                    case 'vg+':
                        $suggested_price *= 1.1;
                        break;
                    case 'very good':
                    case 'vg':
                        $suggested_price *= 0.9;
                        break;
                    case 'good plus':
                    case 'g+':
                        $suggested_price *= 0.7;
                        break;
                    case 'good':
                    case 'g':
                        $suggested_price *= 0.5;
                        break;
                    case 'fair':
                    case 'f':
                        $suggested_price *= 0.3;
                        break;
                    case 'poor':
                    case 'p':
                        $suggested_price *= 0.2;
                        break;
                }
            }
        }

        // Round to 2 decimal places
        $suggested_price = round($suggested_price, 2);

        // Ensure minimum price
        if ($suggested_price < 5.0) {
            $suggested_price = 5.0;
        }

        return $suggested_price;
    }

    /**
     * Set featured image from URL
     */
    private function set_featured_image_from_url($post_id, $image_url) {
        // Check if image URL is valid
        if (empty($image_url)) {
            return false;
        }

        // Get the file
        $response = wp_remote_get($image_url, array(
            'timeout' => 30,
            'headers' => array(
                'User-Agent' => $this->user_agent,
            ),
        ));

        if (is_wp_error($response) || wp_remote_retrieve_response_code($response) !== 200) {
            return false;
        }

        // Get file content
        $image_data = wp_remote_retrieve_body($response);

        // Get file name
        $filename = basename($image_url);

        // Upload the image
        $upload = wp_upload_bits($filename, null, $image_data);

        if ($upload['error']) {
            return false;
        }

        // Get file type
        $wp_filetype = wp_check_filetype($filename, null);

        // Prepare attachment data
        $attachment = array(
            'post_mime_type' => $wp_filetype['type'],
            'post_title' => sanitize_file_name($filename),
            'post_content' => '',
            'post_status' => 'inherit',
        );

        // Insert attachment
        $attachment_id = wp_insert_attachment($attachment, $upload['file'], $post_id);

        if (is_wp_error($attachment_id)) {
            return false;
        }

        // Generate metadata
        require_once(ABSPATH . 'wp-admin/includes/image.php');
        $attachment_data = wp_generate_attachment_metadata($attachment_id, $upload['file']);
        wp_update_attachment_metadata($attachment_id, $attachment_data);

        // Set as featured image
        set_post_thumbnail($post_id, $attachment_id);

        return $attachment_id;
    }
}
