<?php
/**
 * Template Functions
 *
 * Functions for rendering consistent components across the theme
 */

if (!function_exists('rewindrecords_render_record_item')) {
    /**
     * Render a single record item with consistent styling
     *
     * @param int|WP_Post $post Post ID or post object
     * @param array $args Optional. Additional arguments.
     *                    'image_size' - Image size to use (default: 'record-cover')
     *                    'show_artist' - Whether to show artist (default: true)
     *                    'show_year' - Whether to show year (default: true)
     *                    'show_format' - Whether to show format (default: false)
     *                    'show_price' - Whether to show price (default: true, but also depends on global setting)
     *                    'lazy_load' - Whether to lazy load images (default: true)
     * @return void
     */
    function rewindrecords_render_record_item($post = null, $args = array()) {
        $post = get_post($post);
        if (!$post) {
            return;
        }

        // Default arguments
        $defaults = array(
            'image_size' => 'record-cover-optimized',
            'show_artist' => false,
            'show_year' => false,
            'show_format' => true,
            'show_price' => true,
            'lazy_load' => true,
        );

        $args = wp_parse_args($args, $defaults);

        // Get post data
        $post_id = $post->ID;
        $permalink = get_permalink($post_id);
        $title = get_the_title($post_id);

        // Get featured image
        $has_thumbnail = has_post_thumbnail($post_id);
        $thumbnail_url = $has_thumbnail ? get_the_post_thumbnail_url($post_id, $args['image_size']) : '';

        // Get taxonomies
        $artists = get_the_terms($post_id, 'record_artist');
        $artist_name = '';
        if ($artists && !is_wp_error($artists) && !empty($artists)) {
            $artist_name = $artists[0]->name;
        }

        $formats = get_the_terms($post_id, 'record_format');
        $format_name = '';
        if ($formats && !is_wp_error($formats) && !empty($formats)) {
            $format_name = $formats[0]->name;
        }

        // Get meta data
        $year = get_post_meta($post_id, '_record_year', true);
        $price = get_post_meta($post_id, '_record_price', true);
        $show_prices = function_exists('rewindrecords_show_prices') ? rewindrecords_show_prices() : true;

        // Start output
        ?>
        <div class="recordItem">
            <a href="<?php echo esc_url($permalink); ?>" class="recordLink">
                <div class="recordCover">
                    <?php if ($has_thumbnail) : ?>
                        <?php if ($args['lazy_load']) : ?>
                            <img class="lazy" data-src="<?php echo esc_url($thumbnail_url); ?>" alt="<?php echo esc_attr($title); ?>">
                        <?php else : ?>
                            <img src="<?php echo esc_url($thumbnail_url); ?>" alt="<?php echo esc_attr($title); ?>">
                        <?php endif; ?>
                    <?php else : ?>
                        <div class="defaultCover">
                            <svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" viewBox="0 0 100 100">
                                <circle cx="50" cy="50" r="45" fill="#1A1A1A" />
                                <circle cx="50" cy="50" r="42" fill="#333333" />
                                <circle cx="50" cy="50" r="18" fill="#F5D042" />
                                <circle cx="50" cy="50" r="3" fill="#1A1A1A" />
                            </svg>
                        </div>
                    <?php endif; ?>
                </div>
                <div class="recordInfo">
                    <h3 class="recordTitle smallTitle"><?php echo esc_html($title); ?></h3>

                    <?php if ($args['show_artist'] && $artist_name) : ?>
                        <div class="recordArtist"><?php echo esc_html($artist_name); ?></div>
                    <?php endif; ?>

                    <?php if ($args['show_year'] && $year) : ?>
                        <div class="recordYear"><?php echo esc_html($year); ?></div>
                    <?php endif; ?>

                    <?php if ($args['show_format'] && $format_name) : ?>
                        <div class="recordFormat smallTitle primary"><?php echo esc_html($format_name); ?></div>
                    <?php endif; ?>

                    <?php if ($args['show_price'] && $price && $show_prices) : ?>
                        <div class="recordPrice smallTitle primary">€<?php echo number_format((float)$price, 2, ',', '.'); ?></div>
                    <?php endif; ?>
                </div>
            </a>
        </div>
        <?php
    }
}

if (!function_exists('rewindrecords_render_record_loop')) {
    /**
     * Render a loop of record items
     *
     * @param WP_Query $query The query object
     * @param array $args Optional. Additional arguments to pass to rewindrecords_render_record_item()
     * @return void
     */
    function rewindrecords_render_record_loop($query, $args = array()) {
        if ($query->have_posts()) {
            while ($query->have_posts()) {
                $query->the_post();
                rewindrecords_render_record_item(get_the_ID(), $args);
            }
        } else {
            ?>
            <div class="noRecords">
                <p><?php _e('No records found.', 'rewindrecords'); ?></p>
            </div>
            <?php
        }
        wp_reset_postdata();
    }
}
