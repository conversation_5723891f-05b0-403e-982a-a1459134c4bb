<?php
/**
 * Custom Post Types for Rewindrecords
 */

// Register Record Custom Post Type
function rewindrecords_register_post_types() {

    // Record Custom Post Type
    $labels = array(
        'name'                  => _x('Records', 'Post Type General Name', 'rewindrecords'),
        'singular_name'         => _x('Record', 'Post Type Singular Name', 'rewindrecords'),
        'menu_name'             => __('Records', 'rewindrecords'),
        'name_admin_bar'        => __('Record', 'rewindrecords'),
        'archives'              => __('Record Archives', 'rewindrecords'),
        'attributes'            => __('Record Attributes', 'rewindrecords'),
        'parent_item_colon'     => __('Parent Record:', 'rewindrecords'),
        'all_items'             => __('All Records', 'rewindrecords'),
        'add_new_item'          => __('Add New Record', 'rewindrecords'),
        'add_new'               => __('Add New', 'rewindrecords'),
        'new_item'              => __('New Record', 'rewindrecords'),
        'edit_item'             => __('Edit Record', 'rewindrecords'),
        'update_item'           => __('Update Record', 'rewindrecords'),
        'view_item'             => __('View Record', 'rewindrecords'),
        'view_items'            => __('View Records', 'rewindrecords'),
        'search_items'          => __('Search Record', 'rewindrecords'),
        'not_found'             => __('Not found', 'rewindrecords'),
        'not_found_in_trash'    => __('Not found in Trash', 'rewindrecords'),
        'featured_image'        => __('Record Cover', 'rewindrecords'),
        'set_featured_image'    => __('Set record cover', 'rewindrecords'),
        'remove_featured_image' => __('Remove record cover', 'rewindrecords'),
        'use_featured_image'    => __('Use as record cover', 'rewindrecords'),
        'insert_into_item'      => __('Insert into record', 'rewindrecords'),
        'uploaded_to_this_item' => __('Uploaded to this record', 'rewindrecords'),
        'items_list'            => __('Records list', 'rewindrecords'),
        'items_list_navigation' => __('Records list navigation', 'rewindrecords'),
        'filter_items_list'     => __('Filter records list', 'rewindrecords'),
    );

    $args = array(
        'label'                 => __('Record', 'rewindrecords'),
        'description'           => __('Vinyl records and albums', 'rewindrecords'),
        'labels'                => $labels,
        'supports'              => array('title', 'editor', 'thumbnail', 'excerpt', 'custom-fields'),
        'taxonomies'            => array('record_genre', 'record_artist', 'record_format'),
        'hierarchical'          => false,
        'public'                => true,
        'show_ui'               => true,
        'show_in_menu'          => true,
        'menu_position'         => 5,
        'menu_icon'             => 'dashicons-album',
        'show_in_admin_bar'     => true,
        'show_in_nav_menus'     => true,
        'can_export'            => true,
        'has_archive'           => true,
        'exclude_from_search'   => false,
        'publicly_queryable'    => true,
        'capability_type'       => 'post',
        'show_in_rest'          => true,
        'rest_base'             => 'records',
    );

    register_post_type('record', $args);
}
add_action('init', 'rewindrecords_register_post_types', 0);

// Register Custom Taxonomies
function rewindrecords_register_taxonomies() {

    // Genre Taxonomy
    $labels = array(
        'name'                       => _x('Genres', 'Taxonomy General Name', 'rewindrecords'),
        'singular_name'              => _x('Genre', 'Taxonomy Singular Name', 'rewindrecords'),
        'menu_name'                  => __('Genres', 'rewindrecords'),
        'all_items'                  => __('All Genres', 'rewindrecords'),
        'parent_item'                => __('Parent Genre', 'rewindrecords'),
        'parent_item_colon'          => __('Parent Genre:', 'rewindrecords'),
        'new_item_name'              => __('New Genre Name', 'rewindrecords'),
        'add_new_item'               => __('Add New Genre', 'rewindrecords'),
        'edit_item'                  => __('Edit Genre', 'rewindrecords'),
        'update_item'                => __('Update Genre', 'rewindrecords'),
        'view_item'                  => __('View Genre', 'rewindrecords'),
        'separate_items_with_commas' => __('Separate genres with commas', 'rewindrecords'),
        'add_or_remove_items'        => __('Add or remove genres', 'rewindrecords'),
        'choose_from_most_used'      => __('Choose from the most used', 'rewindrecords'),
        'popular_items'              => __('Popular Genres', 'rewindrecords'),
        'search_items'               => __('Search Genres', 'rewindrecords'),
        'not_found'                  => __('Not Found', 'rewindrecords'),
        'no_terms'                   => __('No genres', 'rewindrecords'),
        'items_list'                 => __('Genres list', 'rewindrecords'),
        'items_list_navigation'      => __('Genres list navigation', 'rewindrecords'),
    );

    $args = array(
        'labels'                     => $labels,
        'hierarchical'               => true,
        'public'                     => true,
        'show_ui'                    => true,
        'show_admin_column'          => true,
        'show_in_nav_menus'          => true,
        'show_tagcloud'              => true,
        'show_in_rest'               => true,
    );

    register_taxonomy('record_genre', array('record'), $args);

    // Artist Taxonomy
    $labels = array(
        'name'                       => _x('Artists', 'Taxonomy General Name', 'rewindrecords'),
        'singular_name'              => _x('Artist', 'Taxonomy Singular Name', 'rewindrecords'),
        'menu_name'                  => __('Artists', 'rewindrecords'),
        'all_items'                  => __('All Artists', 'rewindrecords'),
        'parent_item'                => __('Parent Artist', 'rewindrecords'),
        'parent_item_colon'          => __('Parent Artist:', 'rewindrecords'),
        'new_item_name'              => __('New Artist Name', 'rewindrecords'),
        'add_new_item'               => __('Add New Artist', 'rewindrecords'),
        'edit_item'                  => __('Edit Artist', 'rewindrecords'),
        'update_item'                => __('Update Artist', 'rewindrecords'),
        'view_item'                  => __('View Artist', 'rewindrecords'),
        'separate_items_with_commas' => __('Separate artists with commas', 'rewindrecords'),
        'add_or_remove_items'        => __('Add or remove artists', 'rewindrecords'),
        'choose_from_most_used'      => __('Choose from the most used', 'rewindrecords'),
        'popular_items'              => __('Popular Artists', 'rewindrecords'),
        'search_items'               => __('Search Artists', 'rewindrecords'),
        'not_found'                  => __('Not Found', 'rewindrecords'),
        'no_terms'                   => __('No artists', 'rewindrecords'),
        'items_list'                 => __('Artists list', 'rewindrecords'),
        'items_list_navigation'      => __('Artists list navigation', 'rewindrecords'),
    );

    $args = array(
        'labels'                     => $labels,
        'hierarchical'               => false,
        'public'                     => true,
        'show_ui'                    => true,
        'show_admin_column'          => true,
        'show_in_nav_menus'          => true,
        'show_tagcloud'              => true,
        'show_in_rest'               => true,
    );

    register_taxonomy('record_artist', array('record'), $args);

    // Format Taxonomy
    $labels = array(
        'name'                       => _x('Formats', 'Taxonomy General Name', 'rewindrecords'),
        'singular_name'              => _x('Format', 'Taxonomy Singular Name', 'rewindrecords'),
        'menu_name'                  => __('Formats', 'rewindrecords'),
        'all_items'                  => __('All Formats', 'rewindrecords'),
        'parent_item'                => __('Parent Format', 'rewindrecords'),
        'parent_item_colon'          => __('Parent Format:', 'rewindrecords'),
        'new_item_name'              => __('New Format Name', 'rewindrecords'),
        'add_new_item'               => __('Add New Format', 'rewindrecords'),
        'edit_item'                  => __('Edit Format', 'rewindrecords'),
        'update_item'                => __('Update Format', 'rewindrecords'),
        'view_item'                  => __('View Format', 'rewindrecords'),
        'separate_items_with_commas' => __('Separate formats with commas', 'rewindrecords'),
        'add_or_remove_items'        => __('Add or remove formats', 'rewindrecords'),
        'choose_from_most_used'      => __('Choose from the most used', 'rewindrecords'),
        'popular_items'              => __('Popular Formats', 'rewindrecords'),
        'search_items'               => __('Search Formats', 'rewindrecords'),
        'not_found'                  => __('Not Found', 'rewindrecords'),
        'no_terms'                   => __('No formats', 'rewindrecords'),
        'items_list'                 => __('Formats list', 'rewindrecords'),
        'items_list_navigation'      => __('Formats list navigation', 'rewindrecords'),
    );

    $args = array(
        'labels'                     => $labels,
        'hierarchical'               => true,
        'public'                     => true,
        'show_ui'                    => true,
        'show_admin_column'          => true,
        'show_in_nav_menus'          => true,
        'show_tagcloud'              => true,
        'show_in_rest'               => true,
    );

    register_taxonomy('record_format', array('record'), $args);

    // Product Type Taxonomy
    $labels = array(
        'name'                       => _x('Product Types', 'Taxonomy General Name', 'rewindrecords'),
        'singular_name'              => _x('Product Type', 'Taxonomy Singular Name', 'rewindrecords'),
        'menu_name'                  => __('Product Types', 'rewindrecords'),
        'all_items'                  => __('All Product Types', 'rewindrecords'),
        'parent_item'                => __('Parent Product Type', 'rewindrecords'),
        'parent_item_colon'          => __('Parent Product Type:', 'rewindrecords'),
        'new_item_name'              => __('New Product Type Name', 'rewindrecords'),
        'add_new_item'               => __('Add New Product Type', 'rewindrecords'),
        'edit_item'                  => __('Edit Product Type', 'rewindrecords'),
        'update_item'                => __('Update Product Type', 'rewindrecords'),
        'view_item'                  => __('View Product Type', 'rewindrecords'),
        'separate_items_with_commas' => __('Separate product types with commas', 'rewindrecords'),
        'add_or_remove_items'        => __('Add or remove product types', 'rewindrecords'),
        'choose_from_most_used'      => __('Choose from the most used', 'rewindrecords'),
        'popular_items'              => __('Popular Product Types', 'rewindrecords'),
        'search_items'               => __('Search Product Types', 'rewindrecords'),
        'not_found'                  => __('Not Found', 'rewindrecords'),
        'no_terms'                   => __('No product types', 'rewindrecords'),
        'items_list'                 => __('Product Types list', 'rewindrecords'),
        'items_list_navigation'      => __('Product Types list navigation', 'rewindrecords'),
    );

    $args = array(
        'labels'                     => $labels,
        'hierarchical'               => true,
        'public'                     => true,
        'show_ui'                    => true,
        'show_admin_column'          => true,
        'show_in_nav_menus'          => true,
        'show_tagcloud'              => true,
        'show_in_rest'               => true,
    );

    register_taxonomy('record_product_type', array('record'), $args);
}
add_action('init', 'rewindrecords_register_taxonomies', 0);

/**
 * Add default terms for product type taxonomy
 */
function rewindrecords_add_default_product_types() {
    // Check if the terms already exist
    $album_term = term_exists('album', 'record_product_type');
    $other_term = term_exists('other', 'record_product_type');

    // If the terms don't exist, create them
    if (!$album_term) {
        wp_insert_term(
            'Album',
            'record_product_type',
            array(
                'description' => __('Music albums and records', 'rewindrecords'),
                'slug' => 'album'
            )
        );
    }

    if (!$other_term) {
        wp_insert_term(
            'Other',
            'record_product_type',
            array(
                'description' => __('Other products like games, merchandise, etc.', 'rewindrecords'),
                'slug' => 'other'
            )
        );
    }
}
add_action('init', 'rewindrecords_add_default_product_types');
