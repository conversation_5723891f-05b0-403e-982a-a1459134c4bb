<?php
/**
 * Records Customizer Settings
 */

class Rewindrecords_Customizer {

    /**
     * Constructor
     */
    public function __construct() {
        add_action('customize_register', array($this, 'register_customizer_settings'));
    }

    /**
     * Register customizer settings
     */
    public function register_customizer_settings($wp_customize) {
        // Add Records section
        $wp_customize->add_section('rewindrecords_options', array(
            'title'    => __('Rewindrecords Options', 'rewindrecords'),
            'priority' => 120,
        ));

        // Add Show Prices setting
        $wp_customize->add_setting('rewindrecords_show_prices', array(
            'default'           => true,
            'sanitize_callback' => 'rewindrecords_sanitize_checkbox',
            'transport'         => 'refresh',
        ));

        // Add Show Prices control
        $wp_customize->add_control('rewindrecords_show_prices', array(
            'label'       => __('Show Record Prices', 'rewindrecords'),
            'description' => __('Enable or disable the display of record prices throughout the site.', 'rewindrecords'),
            'section'     => 'rewindrecords_options',
            'type'        => 'checkbox',
            'priority'    => 10,
        ));

        // Add Store Location section
        $wp_customize->add_section('rewindrecords_location', array(
            'title'    => __('Store Location', 'rewindrecords'),
            'priority' => 130,
        ));

        // Add Store Address setting
        $wp_customize->add_setting('rewindrecords_store_address', array(
            'default'           => '',
            'sanitize_callback' => 'sanitize_text_field',
            'transport'         => 'refresh',
        ));

        // Add Store Address control
        $wp_customize->add_control('rewindrecords_store_address', array(
            'label'       => __('Store Address', 'rewindrecords'),
            'description' => __('Enter the full address of your store.', 'rewindrecords'),
            'section'     => 'rewindrecords_location',
            'type'        => 'text',
            'priority'    => 10,
        ));

        // Add Store City setting
        $wp_customize->add_setting('rewindrecords_store_city', array(
            'default'           => '',
            'sanitize_callback' => 'sanitize_text_field',
            'transport'         => 'refresh',
        ));

        // Add Store City control
        $wp_customize->add_control('rewindrecords_store_city', array(
            'label'       => __('City', 'rewindrecords'),
            'section'     => 'rewindrecords_location',
            'type'        => 'text',
            'priority'    => 20,
        ));

        // Add Store Postal Code setting
        $wp_customize->add_setting('rewindrecords_store_postal_code', array(
            'default'           => '',
            'sanitize_callback' => 'sanitize_text_field',
            'transport'         => 'refresh',
        ));

        // Add Store Postal Code control
        $wp_customize->add_control('rewindrecords_store_postal_code', array(
            'label'       => __('Postal Code', 'rewindrecords'),
            'section'     => 'rewindrecords_location',
            'type'        => 'text',
            'priority'    => 30,
        ));

        // Add Google Maps Link setting
        $wp_customize->add_setting('rewindrecords_google_maps_link', array(
            'default'           => '',
            'sanitize_callback' => 'esc_url_raw',
            'transport'         => 'refresh',
        ));

        // Add Google Maps Link control
        $wp_customize->add_control('rewindrecords_google_maps_link', array(
            'label'       => __('Google Maps Link', 'rewindrecords'),
            'description' => __('Enter the Google Maps URL for your store location.', 'rewindrecords'),
            'section'     => 'rewindrecords_location',
            'type'        => 'url',
            'priority'    => 40,
        ));

        // Add Maintenance Mode section
        $wp_customize->add_section('rewindrecords_maintenance', array(
            'title'    => __('Maintenance Mode', 'rewindrecords'),
            'priority' => 140,
        ));

        // Add Maintenance Mode setting
        $wp_customize->add_setting('rewindrecords_maintenance_mode', array(
            'default'           => false,
            'sanitize_callback' => 'rewindrecords_sanitize_checkbox',
            'transport'         => 'refresh',
        ));

        // Add Maintenance Mode control
        $wp_customize->add_control('rewindrecords_maintenance_mode', array(
            'label'       => __('Enable Maintenance Mode', 'rewindrecords'),
            'description' => __('When enabled, visitors will see a maintenance page instead of your website.', 'rewindrecords'),
            'section'     => 'rewindrecords_maintenance',
            'type'        => 'checkbox',
            'priority'    => 10,
        ));

        // Add Maintenance Logo setting
        $wp_customize->add_setting('rewindrecords_maintenance_logo', array(
            'default'           => '',
            'sanitize_callback' => 'absint',
            'transport'         => 'refresh',
        ));

        // Add Maintenance Logo control
        $wp_customize->add_control(new WP_Customize_Media_Control($wp_customize, 'rewindrecords_maintenance_logo', array(
            'label'       => __('Logo', 'rewindrecords'),
            'description' => __('Upload a logo to display on the maintenance page.', 'rewindrecords'),
            'section'     => 'rewindrecords_maintenance',
            'mime_type'   => 'image',
            'priority'    => 20,
        )));

        // Add Maintenance Title setting
        $wp_customize->add_setting('rewindrecords_maintenance_title', array(
            'default'           => __('Coming Soon', 'rewindrecords'),
            'sanitize_callback' => 'sanitize_text_field',
            'transport'         => 'refresh',
        ));

        // Add Maintenance Title control
        $wp_customize->add_control('rewindrecords_maintenance_title', array(
            'label'       => __('Title', 'rewindrecords'),
            'description' => __('Enter the title for the maintenance page.', 'rewindrecords'),
            'section'     => 'rewindrecords_maintenance',
            'type'        => 'text',
            'priority'    => 30,
        ));

        // Add Maintenance Message setting
        $wp_customize->add_setting('rewindrecords_maintenance_message', array(
            'default'           => __('We are currently working on our website. Please check back soon!', 'rewindrecords'),
            'sanitize_callback' => 'wp_kses_post',
            'transport'         => 'refresh',
        ));

        // Add Maintenance Message control
        $wp_customize->add_control('rewindrecords_maintenance_message', array(
            'label'       => __('Message', 'rewindrecords'),
            'description' => __('Enter a message to display on the maintenance page.', 'rewindrecords'),
            'section'     => 'rewindrecords_maintenance',
            'type'        => 'textarea',
            'priority'    => 40,
        ));

        // Add Show Location setting
        $wp_customize->add_setting('rewindrecords_maintenance_show_location', array(
            'default'           => true,
            'sanitize_callback' => 'rewindrecords_sanitize_checkbox',
            'transport'         => 'refresh',
        ));

        // Add Show Location control
        $wp_customize->add_control('rewindrecords_maintenance_show_location', array(
            'label'       => __('Show Location', 'rewindrecords'),
            'description' => __('Display the store location on the maintenance page.', 'rewindrecords'),
            'section'     => 'rewindrecords_maintenance',
            'type'        => 'checkbox',
            'priority'    => 50,
        ));

        // Add Background Color setting
        $wp_customize->add_setting('rewindrecords_maintenance_bg_color', array(
            'default'           => '#1A1A1A',
            'sanitize_callback' => 'sanitize_hex_color',
            'transport'         => 'refresh',
        ));

        // Add Background Color control
        $wp_customize->add_control(new WP_Customize_Color_Control($wp_customize, 'rewindrecords_maintenance_bg_color', array(
            'label'       => __('Background Color', 'rewindrecords'),
            'description' => __('Choose a background color for the maintenance page.', 'rewindrecords'),
            'section'     => 'rewindrecords_maintenance',
            'priority'    => 60,
        )));
    }
}

/**
 * Sanitize checkbox values
 */
function rewindrecords_sanitize_checkbox($checked) {
    return ((isset($checked) && true == $checked) ? true : false);
}

/**
 * Check if prices should be shown
 */
function rewindrecords_show_prices() {
    return get_theme_mod('rewindrecords_show_prices', true);
}

/**
 * Get store location information
 */
function rewindrecords_get_location() {
    $location = array(
        'address'     => get_theme_mod('rewindrecords_store_address', ''),
        'street'      => get_theme_mod('customTheme-main-callout-street', ''),
        'city'        => get_theme_mod('customTheme-main-callout-city', ''),
        'postal_code' => get_theme_mod('rewindrecords_store_postal_code', ''),
        'maps_link'   => get_theme_mod('rewindrecords_google_maps_link', ''),
    );

    // Create a formatted address string
    $formatted_address = '';

    // First try to use the new street field
    if (!empty($location['street'])) {
        $formatted_address .= $location['street'];
    }
    // Fall back to the old address field if street is empty
    elseif (!empty($location['address'])) {
        $formatted_address .= $location['address'];
    }

    if (!empty($location['postal_code']) && !empty($location['city'])) {
        if (!empty($formatted_address)) {
            $formatted_address .= ', ';
        }
        $formatted_address .= $location['postal_code'] . ' ' . $location['city'];
    } elseif (!empty($location['city'])) {
        if (!empty($formatted_address)) {
            $formatted_address .= ', ';
        }
        $formatted_address .= $location['city'];
    }

    $location['formatted_address'] = $formatted_address;

    return $location;
}

/**
 * Check if maintenance mode is enabled
 */
function rewindrecords_is_maintenance_mode() {
    // Always allow admin users to view the site
    if (current_user_can('manage_options') || is_admin() || wp_doing_ajax() || wp_doing_cron()) {
        return false;
    }

    return get_theme_mod('rewindrecords_maintenance_mode', false);
}

/**
 * Get maintenance mode settings
 */
function rewindrecords_get_maintenance_settings() {
    $settings = array(
        'logo_id'      => get_theme_mod('rewindrecords_maintenance_logo', ''),
        'title'        => get_theme_mod('rewindrecords_maintenance_title', __('Coming Soon', 'rewindrecords')),
        'message'      => get_theme_mod('rewindrecords_maintenance_message', __('We are currently working on our website. Please check back soon!', 'rewindrecords')),
        'show_location' => get_theme_mod('rewindrecords_maintenance_show_location', true),
        'bg_color'     => get_theme_mod('rewindrecords_maintenance_bg_color', '#1A1A1A'),
    );

    // Get logo URL if set
    if (!empty($settings['logo_id'])) {
        $logo = wp_get_attachment_image_src($settings['logo_id'], 'full');
        $settings['logo_url'] = $logo ? $logo[0] : '';
    } else {
        $settings['logo_url'] = '';
    }

    return $settings;
}
