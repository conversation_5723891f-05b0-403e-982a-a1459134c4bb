<?php
/**
 * Records Meta Boxes
 */

class Rewindrecords_Records_Meta {

    /**
     * Constructor
     */
    public function __construct() {
        add_action('add_meta_boxes', array($this, 'add_meta_boxes'));
        add_action('save_post', array($this, 'save_meta_boxes'), 10, 2);
    }

    /**
     * Add meta boxes
     */
    public function add_meta_boxes() {
        add_meta_box(
            'record_price_meta_box',
            __('Record Price', 'rewindrecords'),
            array($this, 'render_price_meta_box'),
            'record',
            'side',
            'high'
        );
    }

    /**
     * Render price meta box
     */
    public function render_price_meta_box($post) {
        // Add nonce for security
        wp_nonce_field('record_price_meta_box', 'record_price_meta_box_nonce');

        // Get current values
        $price = get_post_meta($post->ID, '_record_price', true);
        $stock = get_post_meta($post->ID, '_record_stock', true);

        ?>
        <p>
            <label for="record_price"><?php _e('Price (€):', 'rewindrecords'); ?></label>
            <input type="number" id="record_price" name="record_price" value="<?php echo esc_attr($price); ?>" step="0.01" min="0" style="width: 100%;">
        </p>
        <p class="description">
            <?php _e('Enter the price including VAT for this record.', 'rewindrecords'); ?>
        </p>
        <p>
            <label for="record_stock"><?php _e('Stock:', 'rewindrecords'); ?></label>
            <input type="number" id="record_stock" name="record_stock" value="<?php echo esc_attr($stock); ?>" min="0" style="width: 100%;">
        </p>
        <p class="description">
            <?php _e('Enter the current stock quantity for this record.', 'rewindrecords'); ?>
        </p>
        <?php
    }

    /**
     * Save meta box data
     */
    public function save_meta_boxes($post_id, $post) {
        // Check if this is an autosave
        if (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE) {
            return;
        }

        // Check post type
        if ($post->post_type !== 'record') {
            return;
        }

        // Check user permissions
        if (!current_user_can('edit_post', $post_id)) {
            return;
        }

        // Check nonce
        if (!isset($_POST['record_price_meta_box_nonce']) || !wp_verify_nonce($_POST['record_price_meta_box_nonce'], 'record_price_meta_box')) {
            return;
        }

        // Save price
        if (isset($_POST['record_price'])) {
            $price = floatval($_POST['record_price']);
            update_post_meta($post_id, '_record_price', $price);
        }

        // Save stock
        if (isset($_POST['record_stock'])) {
            $stock = intval($_POST['record_stock']);
            update_post_meta($post_id, '_record_stock', $stock);
        }
    }
}
