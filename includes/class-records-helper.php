<?php
/**
 * Records Helper Class
 * 
 * Contains helper functions used across multiple classes
 */

class Rewindrecords_Helper {
    
    /**
     * Translate genre from English to Dutch
     *
     * @param string $genre Genre in English
     * @return string Genre in Dutch
     */
    public static function translate_genre_to_dutch($genre) {
        $translations = array(
            'Rock' => 'Rock',
            'Pop' => 'Pop',
            'Jazz' => 'Jazz',
            'Classical' => 'Klassiek',
            'Electronic' => 'Elektronisch',
            'Dance' => 'Dance',
            'Hip-Hop/Rap' => 'Hip-Hop/Rap',
            'Hip Hop/Rap' => 'Hip-Hop/Rap',
            'R&B/Soul' => 'R&B/Soul',
            'Blues' => 'Blues',
            'Country' => 'Country',
            'Folk' => 'Folk',
            'Reggae' => 'Reggae',
            'World' => 'Wereldmuziek',
            'Latin' => 'Latin',
            'Alternative' => 'Alternatief',
            'Metal' => 'Metal',
            'Punk' => 'Punk',
            'Soundtrack' => 'Soundtrack',
            'Vocal' => 'Vocaal',
            'New Age' => 'New Age',
            'Christian & Gospel' => 'Christelijk & Gospel',
            'Indie' => 'Indie',
            'Techno' => 'Techno',
            'House' => 'House',
            'Trance' => 'Trance',
            'Ambient' => 'Ambient',
            'Funk' => 'Funk',
            'Disco' => 'Disco',
            'Soul' => 'Soul',
            'Jazz-Funk' => 'Jazz-Funk',
            'Experimental' => 'Experimenteel',
            'Psychedelic' => 'Psychedelisch',
            'Progressive' => 'Progressief',
            'Hard Rock' => 'Hard Rock',
            'Heavy Metal' => 'Heavy Metal',
            'Death Metal' => 'Death Metal',
            'Black Metal' => 'Black Metal',
            'Thrash Metal' => 'Thrash Metal',
            'Doom Metal' => 'Doom Metal',
            'Grunge' => 'Grunge',
            'Hardcore' => 'Hardcore',
            'Post-Punk' => 'Post-Punk',
            'New Wave' => 'New Wave',
            'Synth-Pop' => 'Synth-Pop',
            'Britpop' => 'Britpop',
            'Indie Rock' => 'Indie Rock',
            'Indie Pop' => 'Indie Pop',
            'Lo-Fi' => 'Lo-Fi',
            'Trip-Hop' => 'Trip-Hop',
            'Drum & Bass' => 'Drum & Bass',
            'Dubstep' => 'Dubstep',
            'Breakbeat' => 'Breakbeat',
            'Electro' => 'Electro',
            'IDM' => 'IDM',
            'Trap' => 'Trap',
            'Grime' => 'Grime',
            'Drill' => 'Drill',
            'K-Pop' => 'K-Pop',
            'J-Pop' => 'J-Pop',
            'Anime' => 'Anime',
            'Game' => 'Game',
            'Comedy' => 'Comedy',
            'Spoken Word' => 'Gesproken Woord',
            'Children\'s Music' => 'Kindermuziek',
            'Holiday' => 'Feestdagen',
            'Opera' => 'Opera',
            'Chamber Music' => 'Kamermuziek',
            'Symphony' => 'Symfonie',
            'Instrumental' => 'Instrumentaal',
            'Easy Listening' => 'Easy Listening',
            'Lounge' => 'Lounge',
            'Bossa Nova' => 'Bossa Nova',
            'Salsa' => 'Salsa',
            'Flamenco' => 'Flamenco',
            'Bluegrass' => 'Bluegrass',
            'Gospel' => 'Gospel',
            'Celtic' => 'Keltisch',
            'African' => 'Afrikaans',
            'Asian' => 'Aziatisch',
            'Middle Eastern' => 'Midden-Oosters',
            'Hawaiian' => 'Hawaiiaans',
            'Ska' => 'Ska',
            'Rocksteady' => 'Rocksteady',
            'Dub' => 'Dub',
            'Dancehall' => 'Dancehall',
            'Calypso' => 'Calypso',
            'Soca' => 'Soca',
            'Afrobeat' => 'Afrobeat',
            'Highlife' => 'Highlife',
            'Jùjú' => 'Jùjú',
            'Soukous' => 'Soukous',
            'Zouk' => 'Zouk',
            'Samba' => 'Samba',
            'Forró' => 'Forró',
            'Tango' => 'Tango',
            'Fado' => 'Fado',
            'Ranchera' => 'Ranchera',
            'Mariachi' => 'Mariachi',
            'Tejano' => 'Tejano',
            'Zydeco' => 'Zydeco',
            'Cajun' => 'Cajun',
            'Traditional' => 'Traditioneel',
            'Contemporary' => 'Hedendaags',
        );
        
        // Return translation if available, otherwise return original
        return isset($translations[$genre]) ? $translations[$genre] : $genre;
    }
    
    /**
     * Generate a Dutch description for the album from iTunes data
     *
     * @param int $post_id Post ID
     * @param array $album_data Album data from iTunes
     * @param array $tracks Tracks data from iTunes
     * @return void
     */
    public static function generate_dutch_description_from_itunes($post_id, $album_data, $tracks) {
        // Get basic album info
        $album_title = isset($album_data['collectionName']) ? $album_data['collectionName'] : '';
        $artist_name = isset($album_data['artistName']) ? $album_data['artistName'] : '';
        $genre = isset($album_data['primaryGenreName']) ? self::translate_genre_to_dutch($album_data['primaryGenreName']) : '';
        $release_date = isset($album_data['releaseDate']) ? substr($album_data['releaseDate'], 0, 10) : '';
        $formatted_date = !empty($release_date) ? date_i18n(get_option('date_format'), strtotime($release_date)) : '';
        
        // Get track count
        $track_count = count($tracks);
        
        // Generate description
        $description = '';
        
        if (!empty($album_title) && !empty($artist_name)) {
            $description .= "<p><strong>$album_title</strong> is een album van <strong>$artist_name</strong>";
            
            if (!empty($genre)) {
                $description .= " in het genre $genre";
            }
            
            if (!empty($formatted_date)) {
                $description .= ", uitgebracht op $formatted_date";
            }
            
            $description .= ".</p>";
            
            if ($track_count > 0) {
                $description .= "<p>Het album bevat $track_count nummers";
                
                // Mention a few notable tracks (first, middle, last)
                $notable_tracks = array();
                if (isset($tracks[0]['trackName'])) {
                    $notable_tracks[] = $tracks[0]['trackName'];
                }
                
                $middle_index = floor($track_count / 2);
                if (isset($tracks[$middle_index]['trackName']) && !in_array($tracks[$middle_index]['trackName'], $notable_tracks)) {
                    $notable_tracks[] = $tracks[$middle_index]['trackName'];
                }
                
                $last_index = $track_count - 1;
                if (isset($tracks[$last_index]['trackName']) && !in_array($tracks[$last_index]['trackName'], $notable_tracks)) {
                    $notable_tracks[] = $tracks[$last_index]['trackName'];
                }
                
                if (!empty($notable_tracks)) {
                    $description .= ", waaronder " . implode(', ', $notable_tracks);
                }
                
                $description .= ".</p>";
            }
            
            if (isset($album_data['copyright'])) {
                $description .= "<p><small>" . $album_data['copyright'] . "</small></p>";
            }
            
            // Save the description as post content
            wp_update_post(array(
                'ID' => $post_id,
                'post_content' => $description
            ));
            
            // Mark as having a description
            update_post_meta($post_id, '_record_needs_description', '0');
        }
    }
}
