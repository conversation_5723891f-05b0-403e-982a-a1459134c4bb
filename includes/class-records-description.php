<?php
/**
 * Records Description Generator
 *
 * Generates Dutch album descriptions using OpenAI API
 */

class Rewindrecords_Records_Description {

    /**
     * OpenAI API key
     */
    private $api_key;

    /**
     * Constructor
     */
    public function __construct() {
        // Get API key from options
        $this->api_key = get_option('rewindrecords_openai_api_key', '');

        // Add AJAX handlers
        add_action('wp_ajax_rewindrecords_generate_description', array($this, 'ajax_generate_description'));

        // Add admin menu
        add_action('admin_menu', array($this, 'add_admin_menu'));

        // Register settings
        add_action('admin_init', array($this, 'register_settings'));
    }

    /**
     * Add admin menu
     */
    public function add_admin_menu() {
        add_submenu_page(
            'rewindrecords',
            __('Album Descriptions', 'rewindrecords'),
            __('Album Descriptions', 'rewindrecords'),
            'manage_options',
            'rewindrecords-descriptions',
            array($this, 'display_settings_page')
        );
    }

    /**
     * Register settings
     */
    public function register_settings() {
        register_setting('rewindrecords_descriptions', 'rewindrecords_openai_api_key');

        add_settings_section(
            'rewindrecords_descriptions_section',
            __('OpenAI API Settings', 'rewindrecords'),
            array($this, 'descriptions_section_callback'),
            'rewindrecords_descriptions'
        );

        add_settings_field(
            'rewindrecords_openai_api_key',
            __('API Key', 'rewindrecords'),
            array($this, 'api_key_field_callback'),
            'rewindrecords_descriptions',
            'rewindrecords_descriptions_section'
        );
    }

    /**
     * Descriptions section description
     */
    public function descriptions_section_callback() {
        echo '<p>' . __('Configure OpenAI API settings to generate Dutch album descriptions.', 'rewindrecords') . '</p>';
        echo '<p>' . __('You need an OpenAI API key to use this feature.', 'rewindrecords') . '</p>';
    }

    /**
     * API key field callback
     */
    public function api_key_field_callback() {
        $api_key = $this->api_key;
        echo '<input type="text" name="rewindrecords_openai_api_key" value="' . esc_attr($api_key) . '" class="regular-text">';
        echo '<p class="description">' . __('Your OpenAI API key', 'rewindrecords') . '</p>';
        echo '<button type="button" class="button" id="test-openai-connection">' . __('Test Connection', 'rewindrecords') . '</button>';
        echo '<span id="connection-result" style="margin-left: 10px;"></span>';
    }

    /**
     * Display settings page
     */
    public function display_settings_page() {
        if (!current_user_can('manage_options')) {
            return;
        }

        ?>
        <div class="wrap">
            <h1><?php echo esc_html(get_admin_page_title()); ?></h1>

            <form action="options.php" method="post">
                <?php
                settings_fields('rewindrecords_descriptions');
                do_settings_sections('rewindrecords_descriptions');
                submit_button(__('Save Settings', 'rewindrecords'));
                ?>
            </form>

            <div class="card">
                <h2><?php _e('Generate Album Descriptions', 'rewindrecords'); ?></h2>
                <p><?php _e('This tool will generate Dutch descriptions for albums in your collection.', 'rewindrecords'); ?></p>
                <p><?php _e('The descriptions will include information about the artist, album, genre, and release date.', 'rewindrecords'); ?></p>

                <div id="generate-description-controls">
                    <button type="button" class="button button-primary" id="generate-all-descriptions"><?php _e('Generate All Descriptions', 'rewindrecords'); ?></button>
                </div>

                <div id="generate-description-progress" style="display: none; margin-top: 20px;">
                    <div class="progress-bar" style="height: 20px; background-color: #f1f1f1; margin-bottom: 10px;">
                        <div class="progress" style="width: 0%; height: 100%; background-color: #0073aa;"></div>
                    </div>
                    <p class="progress-status"><?php _e('Generating descriptions...', 'rewindrecords'); ?></p>
                    <p class="progress-count"><span class="current">0</span> / <span class="total">0</span> <?php _e('records processed', 'rewindrecords'); ?></p>
                </div>

                <div id="generate-description-results" style="display: none; margin-top: 20px;">
                    <h3><?php _e('Generation Results', 'rewindrecords'); ?></h3>
                    <p class="generation-summary"></p>
                    <div class="generation-details" style="margin-top: 10px;">
                        <p><strong><?php _e('Records updated:', 'rewindrecords'); ?></strong> <span class="updated-records-count">0</span></p>
                        <p><strong><?php _e('Records skipped:', 'rewindrecords'); ?></strong> <span class="skipped-records-count">0</span></p>
                        <p><strong><?php _e('Total processed:', 'rewindrecords'); ?></strong> <span class="total-records-count">0</span></p>
                    </div>
                </div>
            </div>

            <script>
                jQuery(document).ready(function($) {
                    // Test OpenAI connection
                    $('#test-openai-connection').on('click', function() {
                        var apiKey = $('input[name="rewindrecords_openai_api_key"]').val();
                        var $result = $('#connection-result');

                        if (!apiKey) {
                            $result.html('<span style="color: red;"><?php _e('Please enter an API key first', 'rewindrecords'); ?></span>');
                            return;
                        }

                        $result.html('<span style="color: blue;"><?php _e('Testing...', 'rewindrecords'); ?></span>');

                        $.ajax({
                            url: ajaxurl,
                            type: 'POST',
                            data: {
                                action: 'rewindrecords_test_openai_connection',
                                api_key: apiKey,
                                nonce: '<?php echo wp_create_nonce('rewindrecords_descriptions'); ?>'
                            },
                            success: function(response) {
                                if (response.success) {
                                    $result.html('<span style="color: green;"><?php _e('Connection successful!', 'rewindrecords'); ?></span>');
                                } else {
                                    $result.html('<span style="color: red;">' + response.data + '</span>');
                                }
                            },
                            error: function() {
                                $result.html('<span style="color: red;"><?php _e('Connection failed', 'rewindrecords'); ?></span>');
                            }
                        });
                    });

                    // Generate all descriptions
                    var recordsToProcess = [];
                    var currentIndex = 0;
                    var totalRecords = 0;
                    var updatedRecords = 0;
                    var skippedRecords = 0;

                    $('#generate-all-descriptions').on('click', function() {
                        var $controls = $('#generate-description-controls');
                        var $progress = $('#generate-description-progress');
                        var $results = $('#generate-description-results');

                        // Reset UI
                        $controls.hide();
                        $results.hide();
                        $progress.show();

                        // Get all album records
                        $.ajax({
                            url: ajaxurl,
                            type: 'POST',
                            data: {
                                action: 'rewindrecords_get_all_albums',
                                nonce: '<?php echo wp_create_nonce('rewindrecords_descriptions'); ?>'
                            },
                            success: function(response) {
                                if (response.success) {
                                    recordsToProcess = response.data;
                                    totalRecords = recordsToProcess.length;

                                    // Update progress UI
                                    $('.progress-count .total').text(totalRecords);

                                    // Start processing records
                                    processNextRecord();
                                } else {
                                    $progress.hide();
                                    $controls.show();
                                    alert('<?php _e('Error: Could not retrieve records.', 'rewindrecords'); ?>');
                                }
                            },
                            error: function() {
                                $progress.hide();
                                $controls.show();
                                alert('<?php _e('Error: Could not retrieve records.', 'rewindrecords'); ?>');
                            }
                        });
                    });

                    function processNextRecord() {
                        if (currentIndex >= recordsToProcess.length) {
                            // All records processed
                            completeGeneration();
                            return;
                        }

                        var recordId = recordsToProcess[currentIndex];

                        // Update progress UI
                        var progressPercent = (currentIndex / totalRecords) * 100;
                        $('.progress-bar .progress').css('width', progressPercent + '%');
                        $('.progress-count .current').text(currentIndex + 1);

                        // Process record
                        $.ajax({
                            url: ajaxurl,
                            type: 'POST',
                            data: {
                                action: 'rewindrecords_generate_description',
                                record_id: recordId,
                                nonce: '<?php echo wp_create_nonce('rewindrecords_descriptions'); ?>'
                            },
                            success: function(response) {
                                if (response.success) {
                                    if (response.data.updated) {
                                        updatedRecords++;
                                    } else {
                                        skippedRecords++;
                                    }
                                } else {
                                    skippedRecords++;
                                }

                                // Process next record
                                currentIndex++;
                                processNextRecord();
                            },
                            error: function() {
                                // Process next record even if there's an error
                                skippedRecords++;
                                currentIndex++;
                                processNextRecord();
                            }
                        });
                    }

                    function completeGeneration() {
                        var $progress = $('#generate-description-progress');
                        var $results = $('#generate-description-results');
                        var $controls = $('#generate-description-controls');

                        // Update progress UI to 100%
                        $('.progress-bar .progress').css('width', '100%');

                        // Hide progress and show results
                        $progress.hide();
                        $results.show();
                        $controls.show();

                        // Update results
                        $('.updated-records-count').text(updatedRecords);
                        $('.skipped-records-count').text(skippedRecords);
                        $('.total-records-count').text(totalRecords);

                        // Update summary
                        var summaryText = '<?php _e('Successfully generated descriptions for {0} records.', 'rewindrecords'); ?>';
                        summaryText = summaryText.replace('{0}', updatedRecords);
                        $('.generation-summary').text(summaryText);

                        // Reset variables for next run
                        recordsToProcess = [];
                        currentIndex = 0;
                        totalRecords = 0;
                        updatedRecords = 0;
                        skippedRecords = 0;
                    }
                });
            </script>
        </div>
        <?php
    }

    /**
     * AJAX handler for testing OpenAI connection
     */
    public function ajax_test_openai_connection() {
        // Verify nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'rewindrecords_descriptions')) {
            wp_send_json_error(__('Security check failed', 'rewindrecords'));
        }

        // Get API key
        $api_key = isset($_POST['api_key']) ? sanitize_text_field($_POST['api_key']) : '';

        if (empty($api_key)) {
            wp_send_json_error(__('API key is empty', 'rewindrecords'));
        }

        // Test connection
        $response = $this->test_openai_connection($api_key);

        if (is_wp_error($response)) {
            wp_send_json_error($response->get_error_message());
        }

        wp_send_json_success();
    }

    /**
     * Test OpenAI connection
     *
     * @param string $api_key OpenAI API key
     * @return bool|WP_Error True on success, WP_Error on failure
     */
    private function test_openai_connection($api_key) {
        $args = array(
            'headers' => array(
                'Authorization' => 'Bearer ' . $api_key,
                'Content-Type' => 'application/json',
            ),
            'timeout' => 15,
        );

        $response = wp_remote_get('https://api.openai.com/v1/models', $args);

        if (is_wp_error($response)) {
            return $response;
        }

        $response_code = wp_remote_retrieve_response_code($response);

        if ($response_code !== 200) {
            $body = wp_remote_retrieve_body($response);
            $data = json_decode($body, true);

            if (isset($data['error']['message'])) {
                return new WP_Error('openai_error', $data['error']['message']);
            }

            return new WP_Error('openai_error', __('Unknown error', 'rewindrecords'));
        }

        return true;
    }

    /**
     * AJAX handler for getting all album records
     */
    public function ajax_get_all_albums() {
        // Verify nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'rewindrecords_descriptions')) {
            wp_send_json_error(__('Security check failed', 'rewindrecords'));
        }

        // Get all album records
        $args = array(
            'post_type' => 'record',
            'posts_per_page' => -1,
            'fields' => 'ids',
            'tax_query' => array(
                array(
                    'taxonomy' => 'record_product_type',
                    'field' => 'slug',
                    'terms' => 'album',
                ),
            ),
            // Only get records that don't already have content
            'meta_query' => array(
                'relation' => 'OR',
                array(
                    'key' => '_record_needs_description',
                    'value' => '1',
                    'compare' => '=',
                ),
                array(
                    'key' => '_record_needs_description',
                    'compare' => 'NOT EXISTS',
                ),
            ),
        );

        $query = new WP_Query($args);

        if ($query->have_posts()) {
            wp_send_json_success($query->posts);
        } else {
            wp_send_json_error(__('No albums found that need descriptions', 'rewindrecords'));
        }
    }

    /**
     * AJAX handler for generating album description
     */
    public function ajax_generate_description() {
        // Verify nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'rewindrecords_descriptions')) {
            wp_send_json_error(__('Security check failed', 'rewindrecords'));
        }

        // Get record ID
        $record_id = isset($_POST['record_id']) ? intval($_POST['record_id']) : 0;

        if (!$record_id) {
            wp_send_json_error(__('Invalid record ID', 'rewindrecords'));
        }

        // Get record data
        $post = get_post($record_id);

        if (!$post || $post->post_type !== 'record') {
            wp_send_json_error(__('Record not found', 'rewindrecords'));
        }

        // Generate description
        $result = $this->generate_album_description($record_id);

        if (is_wp_error($result)) {
            wp_send_json_error($result->get_error_message());
        }

        wp_send_json_success(array(
            'updated' => $result,
            'record_id' => $record_id,
        ));
    }

    /**
     * Generate album description
     *
     * @param int $record_id Record ID
     * @return bool|WP_Error True on success, WP_Error on failure
     */
    public function generate_album_description($record_id) {
        // Check if we have an API key
        if (empty($this->api_key)) {
            return new WP_Error('openai_error', __('OpenAI API key is not set', 'rewindrecords'));
        }

        // Get record data
        $post = get_post($record_id);

        if (!$post || $post->post_type !== 'record') {
            return new WP_Error('record_error', __('Record not found', 'rewindrecords'));
        }

        // Get record metadata
        $title = $post->post_title;
        $artist = '';
        $genre = '';
        $release_date = get_post_meta($record_id, '_record_release_date', true);
        $year = '';
        $country = get_post_meta($record_id, '_record_country', true);
        $tracklist = get_post_meta($record_id, '_record_tracklist', true);

        // Get formatted release date
        if (!empty($release_date)) {
            $date_parts = explode('-', $release_date);
            if (count($date_parts) > 0) {
                $year = $date_parts[0];
            }
            $formatted_date = date_i18n(get_option('date_format'), strtotime($release_date));
        } else {
            $year = get_post_meta($record_id, '_record_year', true);
            $formatted_date = $year;
        }

        // Get artist from taxonomy
        $artists = get_the_terms($record_id, 'record_artist');
        if ($artists && !is_wp_error($artists) && !empty($artists)) {
            $artist = $artists[0]->name;
        }

        // Get genre from taxonomy
        $genres = get_the_terms($record_id, 'record_genre');
        if ($genres && !is_wp_error($genres) && !empty($genres)) {
            $genre_names = array();
            foreach ($genres as $genre_term) {
                $genre_names[] = $genre_term->name;
            }
            $genre = implode(', ', $genre_names);
        }

        // Get format from taxonomy
        $format = '';
        $formats = get_the_terms($record_id, 'record_format');
        if ($formats && !is_wp_error($formats) && !empty($formats)) {
            $format_names = array();
            foreach ($formats as $format_term) {
                $format_names[] = $format_term->name;
            }
            $format = implode(', ', $format_names);
        }

        // Get notable tracks
        $notable_tracks = array();
        if (!empty($tracklist) && is_array($tracklist) && count($tracklist) > 0) {
            // Get first track
            if (isset($tracklist[0])) {
                $notable_tracks[] = $tracklist[0]['title'];
            }

            // Get a track from the middle
            $middle_index = floor(count($tracklist) / 2);
            if (isset($tracklist[$middle_index]) && !in_array($tracklist[$middle_index]['title'], $notable_tracks)) {
                $notable_tracks[] = $tracklist[$middle_index]['title'];
            }

            // Get last track
            $last_index = count($tracklist) - 1;
            if (isset($tracklist[$last_index]) && !in_array($tracklist[$last_index]['title'], $notable_tracks)) {
                $notable_tracks[] = $tracklist[$last_index]['title'];
            }
        }

        // Check if we have enough information
        if (empty($artist) || empty($title)) {
            return new WP_Error('record_error', __('Not enough information to generate description', 'rewindrecords'));
        }

        // Generate description
        $description = $this->get_description_from_openai(
            $artist,
            $title,
            $genre,
            $formatted_date,
            $country,
            $format,
            $notable_tracks
        );

        if (is_wp_error($description)) {
            return $description;
        }

        // Update post content
        $post_data = array(
            'ID' => $record_id,
            'post_content' => $description,
        );

        $result = wp_update_post($post_data, true);

        if (is_wp_error($result)) {
            return $result;
        }

        // Mark as having a description
        update_post_meta($record_id, '_record_needs_description', '0');

        return true;
    }

    /**
     * Get description from OpenAI
     *
     * @param string $artist Artist name
     * @param string $title Album title
     * @param string $genre Album genre
     * @param string $release_date Release date
     * @param string $country Country
     * @param string $format Format
     * @param array $notable_tracks Notable tracks
     * @return string|WP_Error Description on success, WP_Error on failure
     */
    private function get_description_from_openai($artist, $title, $genre = '', $release_date = '', $country = '', $format = '', $notable_tracks = array()) {
        // Build prompt
        $prompt = "Schrijf een uitgebreide beschrijving in het Nederlands over het album '$title' van '$artist'";

        if (!empty($genre)) {
            $prompt .= ", genre: $genre";
        }

        if (!empty($release_date)) {
            $prompt .= ", uitgebracht op $release_date";
        }

        if (!empty($country)) {
            $prompt .= " in $country";
        }

        if (!empty($format)) {
            $prompt .= ", formaat: $format";
        }

        $prompt .= ".\n\n";

        // Add notable tracks if available
        if (!empty($notable_tracks)) {
            $prompt .= "Enkele nummers op dit album zijn: " . implode(', ', $notable_tracks) . ".\n\n";
        }

        $prompt .= "Beschrijf de muziekstijl, de impact van het album, de context waarin het werd uitgebracht, en enkele interessante feiten. Noem ook de belangrijkste nummers en wat ze bijzonder maakt. Gebruik een informele, enthousiaste toon die past bij een platenzaak. Structureer de tekst in 2-3 paragrafen. Maximaal 200 woorden.";

        // Build request
        $args = array(
            'headers' => array(
                'Authorization' => 'Bearer ' . $this->api_key,
                'Content-Type' => 'application/json',
            ),
            'timeout' => 30,
            'body' => json_encode(array(
                'model' => 'gpt-3.5-turbo',
                'messages' => array(
                    array(
                        'role' => 'system',
                        'content' => 'Je bent een muziekexpert die informatieve en enthousiaste beschrijvingen schrijft over albums in het Nederlands. Je schrijft voor een platenzaak die vinyl en andere muziekformaten verkoopt. Gebruik een enthousiaste, informele toon die klanten aanspreekt.',
                    ),
                    array(
                        'role' => 'user',
                        'content' => $prompt,
                    ),
                ),
                'temperature' => 0.7,
                'max_tokens' => 800,
            )),
        );

        // Make request
        $response = wp_remote_post('https://api.openai.com/v1/chat/completions', $args);

        if (is_wp_error($response)) {
            return $response;
        }

        $response_code = wp_remote_retrieve_response_code($response);

        if ($response_code !== 200) {
            $body = wp_remote_retrieve_body($response);
            $data = json_decode($body, true);

            if (isset($data['error']['message'])) {
                return new WP_Error('openai_error', $data['error']['message']);
            }

            return new WP_Error('openai_error', __('Unknown error', 'rewindrecords'));
        }

        // Parse response
        $body = wp_remote_retrieve_body($response);
        $data = json_decode($body, true);

        if (empty($data['choices'][0]['message']['content'])) {
            return new WP_Error('openai_error', __('No description generated', 'rewindrecords'));
        }

        // Format description
        $description = $data['choices'][0]['message']['content'];
        $description = wpautop($description);

        return $description;
    }
}
