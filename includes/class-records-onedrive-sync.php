<?php
/**
 * Records OneDrive Sync Class
 * 
 * Handles synchronization of record inventory from OneDrive Excel file
 */

class Rewindrecords_OneDrive_Sync {

    /**
     * OneDrive file URL
     */
    private $onedrive_url = 'https://solitcon-my.sharepoint.com/:x:/g/personal/sander_martens_rewindrecords_nl/Ea6t_vytNjlMjxboM4Nta4YBYu61O_uVCLlXvP-iR4oH1A?e=fX2iUZ';
    
    /**
     * Log file path
     */
    private $log_file;

    /**
     * Constructor
     */
    public function __construct() {
        $this->log_file = wp_upload_dir()['basedir'] . '/record-sync.log';
        
        // Schedule the cron job
        add_action('init', array($this, 'schedule_sync'));
        
        // Register the cron hook
        add_action('rewindrecords_onedrive_sync', array($this, 'sync_records'));
        
        // Add admin menu for manual sync
        add_action('admin_menu', array($this, 'add_admin_menu'));
        
        // Handle AJAX requests for manual sync
        add_action('wp_ajax_manual_onedrive_sync', array($this, 'handle_manual_sync'));
        add_action('wp_ajax_manual_file_sync', array($this, 'handle_manual_file_sync'));
        add_action('wp_ajax_test_onedrive_connection', array($this, 'handle_test_connection'));
        add_action('wp_ajax_debug_onedrive_download', array($this, 'handle_debug_download'));
    }

    /**
     * Schedule the cron job to run every 6 hours
     */
    public function schedule_sync() {
        if (!wp_next_scheduled('rewindrecords_onedrive_sync')) {
            wp_schedule_event(time(), 'sixhourly', 'rewindrecords_onedrive_sync');
        }
    }

    /**
     * Add custom cron interval for 6 hours
     */
    public function add_cron_intervals($schedules) {
        $schedules['sixhourly'] = array(
            'interval' => 6 * HOUR_IN_SECONDS,
            'display' => __('Every 6 Hours', 'rewindrecords')
        );
        return $schedules;
    }

    /**
     * Add admin menu page
     */
    public function add_admin_menu() {
        add_submenu_page(
            'edit.php?post_type=record',
            __('OneDrive Sync', 'rewindrecords'),
            __('OneDrive Sync', 'rewindrecords'),
            'manage_options',
            'rewindrecords-onedrive-sync',
            array($this, 'admin_page')
        );
    }

    /**
     * Render admin page
     */
    public function admin_page() {
        ?>
        <div class="wrap">
            <h1><?php _e('OneDrive Inventory Sync', 'rewindrecords'); ?></h1>
            
            <div class="notice notice-info">
                <p><?php _e('This tool synchronizes your record inventory with the Excel file on OneDrive every 6 hours automatically.', 'rewindrecords'); ?></p>
                <p><?php _e('You can also run the synchronization manually using the button below.', 'rewindrecords'); ?></p>
                <p><strong><?php _e('Important:', 'rewindrecords'); ?></strong> <?php _e('Make sure your Excel file contains columns for: Title, Artist, EAN, and Stock. The system will try to detect the column structure automatically.', 'rewindrecords'); ?></p>
            </div>

            <div class="notice notice-warning">
                <p><strong><?php _e('OneDrive File Setup:', 'rewindrecords'); ?></strong></p>
                <p><?php _e('If the automatic download fails, please ensure:', 'rewindrecords'); ?></p>
                <ul style="margin-left: 20px;">
                    <li><?php _e('The OneDrive file is shared with "Anyone with the link can view"', 'rewindrecords'); ?></li>
                    <li><?php _e('The file is saved in Excel format (.xlsx) or CSV format (.csv)', 'rewindrecords'); ?></li>
                    <li><?php _e('The file contains the required columns: Title, Artist, EAN, Stock', 'rewindrecords'); ?></li>
                </ul>
                <p><strong><?php _e('Alternative Solution:', 'rewindrecords'); ?></strong> <?php _e('If OneDrive sync continues to fail, you can:', 'rewindrecords'); ?></p>
                <ol style="margin-left: 20px;">
                    <li><?php _e('Open your Excel file in OneDrive', 'rewindrecords'); ?></li>
                    <li><?php _e('Go to File > Save As > Download a Copy', 'rewindrecords'); ?></li>
                    <li><?php _e('Save as CSV format', 'rewindrecords'); ?></li>
                    <li><?php _e('Use the manual upload feature below', 'rewindrecords'); ?></li>
                </ol>
            </div>

            <div class="card">
                <h2><?php _e('Sync Status', 'rewindrecords'); ?></h2>
                <p><strong><?php _e('Next Scheduled Sync:', 'rewindrecords'); ?></strong> 
                    <?php 
                    $next_sync = wp_next_scheduled('rewindrecords_onedrive_sync');
                    echo $next_sync ? date('Y-m-d H:i:s', $next_sync) : __('Not scheduled', 'rewindrecords');
                    ?>
                </p>
                <p><strong><?php _e('Last Sync:', 'rewindrecords'); ?></strong> 
                    <?php 
                    $last_sync = get_option('rewindrecords_last_onedrive_sync', __('Never', 'rewindrecords'));
                    echo $last_sync !== 'Never' ? date('Y-m-d H:i:s', $last_sync) : $last_sync;
                    ?>
                </p>
            </div>

            <div class="card">
                <h2><?php _e('Manual Sync', 'rewindrecords'); ?></h2>
                <p><?php _e('Click the button below to run the synchronization immediately.', 'rewindrecords'); ?></p>
                <button id="manual-sync-btn" class="button button-primary"><?php _e('Run Sync Now', 'rewindrecords'); ?></button>

                <button id="test-connection-btn" class="button button-secondary" style="margin-left: 10px;"><?php _e('Test OneDrive Connection', 'rewindrecords'); ?></button>
                <button id="debug-download-btn" class="button button-secondary" style="margin-left: 10px;"><?php _e('Debug Download', 'rewindrecords'); ?></button>

                <h3><?php _e('Alternative: Upload File Manually', 'rewindrecords'); ?></h3>
                <p><?php _e('If the OneDrive sync is not working, you can upload the Excel/CSV file manually:', 'rewindrecords'); ?></p>
                <form id="manual-upload-form" enctype="multipart/form-data">
                    <input type="file" id="manual-file" name="manual_file" accept=".xlsx,.xls,.csv" />
                    <button type="submit" class="button"><?php _e('Upload and Sync', 'rewindrecords'); ?></button>
                </form>
                <div id="sync-progress" style="display: none;">
                    <p><?php _e('Synchronization in progress...', 'rewindrecords'); ?></p>
                    <div class="progress-bar" style="width: 100%; background: #f1f1f1; border-radius: 3px;">
                        <div id="progress-fill" style="width: 0%; height: 20px; background: #0073aa; border-radius: 3px; transition: width 0.3s;"></div>
                    </div>
                </div>
                <div id="sync-results" style="display: none;"></div>
            </div>

            <div class="card">
                <h2><?php _e('Recent Log Entries', 'rewindrecords'); ?></h2>
                <div style="background: #f9f9f9; padding: 10px; border: 1px solid #ddd; max-height: 300px; overflow-y: auto;">
                    <pre><?php echo esc_html($this->get_recent_log_entries()); ?></pre>
                </div>
            </div>
        </div>

        <script>
        jQuery(document).ready(function($) {
            $('#manual-sync-btn').on('click', function() {
                var button = $(this);
                var progress = $('#sync-progress');
                var results = $('#sync-results');

                button.prop('disabled', true);
                progress.show();
                results.hide();

                $.ajax({
                    url: ajaxurl,
                    type: 'POST',
                    data: {
                        action: 'manual_onedrive_sync',
                        nonce: '<?php echo wp_create_nonce('manual_onedrive_sync'); ?>'
                    },
                    success: function(response) {
                        if (response.success) {
                            results.html('<div class="notice notice-success"><p>' + response.data.message + '</p></div>');
                        } else {
                            results.html('<div class="notice notice-error"><p>' + response.data.message + '</p></div>');
                        }
                    },
                    error: function() {
                        results.html('<div class="notice notice-error"><p><?php _e('An error occurred during synchronization.', 'rewindrecords'); ?></p></div>');
                    },
                    complete: function() {
                        button.prop('disabled', false);
                        progress.hide();
                        results.show();
                        // Reload page after 2 seconds to show updated status
                        setTimeout(function() {
                            location.reload();
                        }, 2000);
                    }
                });
            });

            // Handle test connection
            $('#test-connection-btn').on('click', function() {
                var button = $(this);
                var results = $('#sync-results');

                button.prop('disabled', true).text('<?php _e('Testing...', 'rewindrecords'); ?>');
                results.hide();

                $.ajax({
                    url: ajaxurl,
                    type: 'POST',
                    data: {
                        action: 'test_onedrive_connection',
                        nonce: '<?php echo wp_create_nonce('test_onedrive_connection'); ?>'
                    },
                    success: function(response) {
                        if (response.success) {
                            results.html('<div class="notice notice-success"><p>' + response.data.message + '</p></div>');
                        } else {
                            results.html('<div class="notice notice-error"><p>' + response.data.message + '</p></div>');
                        }
                    },
                    error: function() {
                        results.html('<div class="notice notice-error"><p><?php _e('Connection test failed.', 'rewindrecords'); ?></p></div>');
                    },
                    complete: function() {
                        button.prop('disabled', false).text('<?php _e('Test OneDrive Connection', 'rewindrecords'); ?>');
                        results.show();
                    }
                });
            });

            // Handle debug download
            $('#debug-download-btn').on('click', function() {
                var button = $(this);
                var results = $('#sync-results');

                button.prop('disabled', true).text('<?php _e('Debugging...', 'rewindrecords'); ?>');
                results.hide();

                $.ajax({
                    url: ajaxurl,
                    type: 'POST',
                    data: {
                        action: 'debug_onedrive_download',
                        nonce: '<?php echo wp_create_nonce('debug_onedrive_download'); ?>'
                    },
                    success: function(response) {
                        if (response.success) {
                            results.html('<div class="notice notice-info"><p><strong>Debug Results:</strong></p><pre style="background: #f9f9f9; padding: 10px; border: 1px solid #ddd; max-height: 300px; overflow-y: auto;">' + response.data.debug_info + '</pre></div>');
                        } else {
                            results.html('<div class="notice notice-error"><p>' + response.data.message + '</p></div>');
                        }
                    },
                    error: function() {
                        results.html('<div class="notice notice-error"><p><?php _e('Debug failed.', 'rewindrecords'); ?></p></div>');
                    },
                    complete: function() {
                        button.prop('disabled', false).text('<?php _e('Debug Download', 'rewindrecords'); ?>');
                        results.show();
                    }
                });
            });

            // Handle manual file upload
            $('#manual-upload-form').on('submit', function(e) {
                e.preventDefault();

                var fileInput = $('#manual-file')[0];
                var progress = $('#sync-progress');
                var results = $('#sync-results');

                if (!fileInput.files.length) {
                    alert('<?php _e('Please select a file first.', 'rewindrecords'); ?>');
                    return;
                }

                var formData = new FormData();
                formData.append('action', 'manual_file_sync');
                formData.append('nonce', '<?php echo wp_create_nonce('manual_file_sync'); ?>');
                formData.append('sync_file', fileInput.files[0]);

                progress.show();
                results.hide();

                $.ajax({
                    url: ajaxurl,
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function(response) {
                        if (response.success) {
                            results.html('<div class="notice notice-success"><p>' + response.data.message + '</p></div>');
                        } else {
                            results.html('<div class="notice notice-error"><p>' + response.data.message + '</p></div>');
                        }
                    },
                    error: function() {
                        results.html('<div class="notice notice-error"><p><?php _e('An error occurred during file upload.', 'rewindrecords'); ?></p></div>');
                    },
                    complete: function() {
                        progress.hide();
                        results.show();
                        // Reset form
                        $('#manual-upload-form')[0].reset();
                        // Reload page after 2 seconds to show updated status
                        setTimeout(function() {
                            location.reload();
                        }, 2000);
                    }
                });
            });
        });
        </script>
        <?php
    }

    /**
     * Handle manual sync AJAX request
     */
    public function handle_manual_sync() {
        // Check nonce
        if (!wp_verify_nonce($_POST['nonce'], 'manual_onedrive_sync')) {
            wp_send_json_error(array('message' => __('Security check failed.', 'rewindrecords')));
        }

        // Check permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error(array('message' => __('Insufficient permissions.', 'rewindrecords')));
        }

        // Run the sync
        $result = $this->sync_records();

        if ($result['success']) {
            wp_send_json_success(array(
                'message' => sprintf(
                    __('Sync completed successfully. Updated: %d, Added: %d, Set to zero stock: %d', 'rewindrecords'),
                    $result['updated'],
                    $result['added'],
                    $result['zeroed']
                )
            ));
        } else {
            wp_send_json_error(array('message' => $result['message']));
        }
    }

    /**
     * Handle manual file sync AJAX request
     */
    public function handle_manual_file_sync() {
        // Check nonce
        if (!wp_verify_nonce($_POST['nonce'], 'manual_file_sync')) {
            wp_send_json_error(array('message' => __('Security check failed.', 'rewindrecords')));
        }

        // Check permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error(array('message' => __('Insufficient permissions.', 'rewindrecords')));
        }

        // Check if file was uploaded
        if (!isset($_FILES['sync_file']) || $_FILES['sync_file']['error'] !== UPLOAD_ERR_OK) {
            wp_send_json_error(array('message' => __('File upload failed.', 'rewindrecords')));
        }

        $uploaded_file = $_FILES['sync_file'];

        // Validate file type
        $allowed_types = array('xlsx', 'xls', 'csv');
        $file_extension = strtolower(pathinfo($uploaded_file['name'], PATHINFO_EXTENSION));

        if (!in_array($file_extension, $allowed_types)) {
            wp_send_json_error(array('message' => __('Invalid file type. Please upload Excel (.xlsx, .xls) or CSV (.csv) files only.', 'rewindrecords')));
        }

        try {
            // Move uploaded file to temp location
            $temp_file = wp_upload_dir()['basedir'] . '/temp_manual_sync_' . time() . '.' . $file_extension;

            if (!move_uploaded_file($uploaded_file['tmp_name'], $temp_file)) {
                throw new Exception('Failed to move uploaded file');
            }

            // Parse the file
            $excel_data = $this->parse_excel_file($temp_file);

            if (!$excel_data || empty($excel_data)) {
                throw new Exception('Failed to parse uploaded file or file is empty');
            }

            // Process the data
            $result = $this->process_excel_data($excel_data);

            // Clean up temp file
            unlink($temp_file);

            // Update last sync time
            update_option('rewindrecords_last_onedrive_sync', time());

            $this->log('Manual file sync completed successfully. Updated: ' . $result['updated'] . ', Added: ' . $result['added'] . ', Zeroed: ' . $result['zeroed']);

            wp_send_json_success(array(
                'message' => sprintf(
                    __('File sync completed successfully. Updated: %d, Added: %d, Set to zero stock: %d', 'rewindrecords'),
                    $result['updated'],
                    $result['added'],
                    $result['zeroed']
                )
            ));

        } catch (Exception $e) {
            // Clean up temp file if it exists
            if (isset($temp_file) && file_exists($temp_file)) {
                unlink($temp_file);
            }

            $this->log('Manual file sync failed: ' . $e->getMessage());
            wp_send_json_error(array('message' => $e->getMessage()));
        }
    }

    /**
     * Handle test connection AJAX request
     */
    public function handle_test_connection() {
        // Check nonce
        if (!wp_verify_nonce($_POST['nonce'], 'test_onedrive_connection')) {
            wp_send_json_error(array('message' => __('Security check failed.', 'rewindrecords')));
        }

        // Check permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error(array('message' => __('Insufficient permissions.', 'rewindrecords')));
        }

        try {
            $this->log('Testing OneDrive connection...');

            // Try to download just the headers to test connection
            $download_methods = array('convert_url', 'direct_sharepoint', 'embed_method');
            $success = false;
            $last_error = '';

            foreach ($download_methods as $method) {
                try {
                    $this->log("Testing method: {$method}");

                    if ($method === 'convert_url') {
                        $url = $this->convert_onedrive_url($this->onedrive_url);
                    } elseif ($method === 'direct_sharepoint') {
                        $url = str_replace('/:x:/', '/_api/web/GetFileByServerRelativeUrl(\'', $this->onedrive_url);
                        $url = str_replace('?e=', '\')/\$value?e=', $url);
                    } else {
                        $url = str_replace('/:x:/', '/_layouts/15/Doc.aspx?sourcedoc=', $this->onedrive_url);
                        $url .= '&action=embedview&wdDownloadButton=True';
                    }

                    if ($url) {
                        $response = wp_remote_head($url, array(
                            'timeout' => 30,
                            'redirection' => 5, // Follow up to 5 redirects
                            'headers' => array(
                                'User-Agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                            )
                        ));

                        if (!is_wp_error($response)) {
                            $response_code = wp_remote_retrieve_response_code($response);
                            $this->log("Response code for {$method}: {$response_code}");

                            // Accept 200 (OK), 302 (redirect), and 206 (partial content) as success
                            if (in_array($response_code, array(200, 206, 302))) {
                                // For redirects, try to get the final URL
                                if ($response_code === 302) {
                                    $headers = wp_remote_retrieve_headers($response);
                                    $location = isset($headers['location']) ? $headers['location'] : '';
                                    $this->log("Redirect location: {$location}");

                                    // Test the redirect location if available
                                    if ($location) {
                                        $redirect_response = wp_remote_head($location, array(
                                            'timeout' => 15,
                                            'headers' => array(
                                                'User-Agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                                            )
                                        ));

                                        if (!is_wp_error($redirect_response)) {
                                            $redirect_code = wp_remote_retrieve_response_code($redirect_response);
                                            $this->log("Redirect response code: {$redirect_code}");
                                            if (in_array($redirect_code, array(200, 206))) {
                                                $success = true;
                                                $this->log("Connection test successful with method: {$method} (after redirect)");
                                                break;
                                            }
                                        }
                                    }
                                }

                                $success = true;
                                $this->log("Connection test successful with method: {$method}");
                                break;
                            } else {
                                $last_error = "HTTP {$response_code}";
                            }
                        } else {
                            $last_error = $response->get_error_message();
                        }
                    }
                } catch (Exception $e) {
                    $last_error = $e->getMessage();
                    continue;
                }
            }

            if ($success) {
                wp_send_json_success(array(
                    'message' => __('OneDrive connection test successful! The file appears to be accessible.', 'rewindrecords')
                ));
            } else {
                wp_send_json_error(array(
                    'message' => sprintf(
                        __('OneDrive connection test failed. Last error: %s. Please check the file URL and permissions.', 'rewindrecords'),
                        $last_error
                    )
                ));
            }

        } catch (Exception $e) {
            $this->log('Connection test error: ' . $e->getMessage());
            wp_send_json_error(array('message' => $e->getMessage()));
        }
    }

    /**
     * Handle debug download AJAX request
     */
    public function handle_debug_download() {
        // Check nonce
        if (!wp_verify_nonce($_POST['nonce'], 'debug_onedrive_download')) {
            wp_send_json_error(array('message' => __('Security check failed.', 'rewindrecords')));
        }

        // Check permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error(array('message' => __('Insufficient permissions.', 'rewindrecords')));
        }

        try {
            $debug_info = "OneDrive Debug Information\n";
            $debug_info .= "========================\n\n";
            $debug_info .= "Original URL: " . $this->onedrive_url . "\n\n";

            // Test each download method
            $download_methods = array('convert_url', 'direct_sharepoint', 'embed_method', 'csv_export');

            foreach ($download_methods as $method) {
                $debug_info .= "Testing method: {$method}\n";
                $debug_info .= "----------------------------\n";

                try {
                    if ($method === 'convert_url') {
                        $url = $this->convert_onedrive_url($this->onedrive_url);
                        $debug_info .= "Converted URL: " . ($url ?: 'FAILED') . "\n";
                    } elseif ($method === 'direct_sharepoint') {
                        $url_parts = parse_url($this->onedrive_url);
                        $path = $url_parts['path'];
                        $debug_info .= "URL path: {$path}\n";

                        if (preg_match('/\/personal\/([^\/]+)\/Documents\/(.+)/', $path, $matches)) {
                            $user_path = $matches[1];
                            $file_path = $matches[2];
                            $debug_info .= "User path: {$user_path}\n";
                            $debug_info .= "File path: {$file_path}\n";
                        } else {
                            $debug_info .= "Could not extract file path\n";
                        }
                    } elseif ($method === 'embed_method') {
                        $embed_url = str_replace('/:x:/', '/_layouts/15/Doc.aspx?sourcedoc=', $this->onedrive_url);
                        $debug_info .= "Embed URL: {$embed_url}\n";
                    } elseif ($method === 'csv_export') {
                        if (preg_match('/\/([a-zA-Z0-9_-]+)\?e=/', $this->onedrive_url, $matches)) {
                            $file_id = $matches[1];
                            $debug_info .= "File ID: {$file_id}\n";
                            $debug_info .= "Auth key: " . $this->extract_auth_key($this->onedrive_url) . "\n";
                            $debug_info .= "E parameter: " . $this->extract_e_parameter($this->onedrive_url) . "\n";
                        }
                    }

                    $debug_info .= "Status: Method setup completed\n";

                } catch (Exception $e) {
                    $debug_info .= "Error: " . $e->getMessage() . "\n";
                }

                $debug_info .= "\n";
            }

            // Test basic URL accessibility
            $debug_info .= "Basic URL Test\n";
            $debug_info .= "==============\n";
            $response = wp_remote_head($this->onedrive_url, array(
                'timeout' => 15,
                'headers' => array(
                    'User-Agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                )
            ));

            if (is_wp_error($response)) {
                $debug_info .= "Error: " . $response->get_error_message() . "\n";
            } else {
                $debug_info .= "Response code: " . wp_remote_retrieve_response_code($response) . "\n";
                $headers = wp_remote_retrieve_headers($response);
                $debug_info .= "Content-Type: " . (isset($headers['content-type']) ? $headers['content-type'] : 'Not set') . "\n";
                $debug_info .= "Content-Length: " . (isset($headers['content-length']) ? $headers['content-length'] : 'Not set') . "\n";
            }

            wp_send_json_success(array('debug_info' => $debug_info));

        } catch (Exception $e) {
            wp_send_json_error(array('message' => $e->getMessage()));
        }
    }

    /**
     * Get recent log entries
     */
    private function get_recent_log_entries($lines = 50) {
        if (!file_exists($this->log_file)) {
            return __('No log entries found.', 'rewindrecords');
        }

        $file = file($this->log_file);
        if ($file === false) {
            return __('Unable to read log file.', 'rewindrecords');
        }

        $recent_lines = array_slice($file, -$lines);
        return implode('', $recent_lines);
    }

    /**
     * Main sync function
     */
    public function sync_records() {
        $this->log('Starting OneDrive sync...');

        // Increase time limit for large files
        set_time_limit(300); // 5 minutes

        try {
            // Download and parse Excel file
            $excel_data = $this->download_and_parse_excel();

            if (!$excel_data) {
                $error_msg = 'Failed to download or parse Excel file. Please check the OneDrive link and file format.';
                $this->log('ERROR: ' . $error_msg);
                return array('success' => false, 'message' => __($error_msg, 'rewindrecords'));
            }

            $this->log('Successfully parsed Excel file with ' . count($excel_data) . ' records');

            // Validate data before processing
            if (count($excel_data) === 0) {
                $error_msg = 'Excel file appears to be empty or contains no valid data rows.';
                $this->log('ERROR: ' . $error_msg);
                return array('success' => false, 'message' => __($error_msg, 'rewindrecords'));
            }

            // Process the data
            $result = $this->process_excel_data($excel_data);

            // Update last sync time
            update_option('rewindrecords_last_onedrive_sync', time());

            $this->log('Sync completed successfully. Updated: ' . $result['updated'] . ', Added: ' . $result['added'] . ', Zeroed: ' . $result['zeroed']);

            return array(
                'success' => true,
                'updated' => $result['updated'],
                'added' => $result['added'],
                'zeroed' => $result['zeroed']
            );

        } catch (Exception $e) {
            $error_msg = 'Sync failed: ' . $e->getMessage();
            $this->log('ERROR: ' . $error_msg);

            // Provide more helpful error messages
            if (strpos($e->getMessage(), 'timeout') !== false) {
                $error_msg .= ' Try using the manual file upload instead.';
            } elseif (strpos($e->getMessage(), 'HTTP error') !== false) {
                $error_msg .= ' The OneDrive file may not be accessible. Check sharing permissions.';
            }

            return array('success' => false, 'message' => $error_msg);
        }
    }

    /**
     * Download and parse Excel file from OneDrive
     */
    private function download_and_parse_excel() {
        $this->log('Attempting to download file from OneDrive...');

        // Try multiple methods to download the file
        $download_methods = array(
            'convert_url',
            'embed_download',
            'direct_sharepoint',
            'embed_method',
            'csv_export'
        );

        $excel_data = null;
        $last_error = '';

        foreach ($download_methods as $method) {
            try {
                $this->log("Trying download method: {$method}");
                $excel_data = $this->{"download_method_{$method}"}();

                if ($excel_data && !empty($excel_data)) {
                    $this->log("Successfully downloaded and parsed file using method: {$method}");
                    break;
                }
            } catch (Exception $e) {
                $last_error = $e->getMessage();
                $this->log("Method {$method} failed: " . $last_error);
                continue;
            }
        }

        if (!$excel_data) {
            throw new Exception('All download methods failed. Last error: ' . $last_error);
        }

        return $excel_data;
    }

    /**
     * Download method 1: Convert OneDrive URL
     */
    private function download_method_convert_url() {
        $this->log('Converting OneDrive URL to download URL...');
        $download_url = $this->convert_onedrive_url($this->onedrive_url);

        if (!$download_url) {
            throw new Exception('Failed to convert OneDrive URL to download URL');
        }

        $this->log('Converted URL: ' . $download_url);
        return $this->download_and_parse_from_url($download_url);
    }

    /**
     * Download method 2: Embed download method
     */
    private function download_method_embed_download() {
        $this->log('Trying embed download method...');

        // For OneDrive links, try to use the embed view to get the actual file
        // This method tries to extract the download URL from the embed page
        $embed_url = str_replace('/:x:/', '/_layouts/15/Doc.aspx?sourcedoc=', $this->onedrive_url);
        $embed_url = preg_replace('/\?e=.*$/', '', $embed_url) . '&action=embedview';

        $this->log('Embed URL: ' . $embed_url);

        // Get the embed page to extract the actual download URL
        $response = wp_remote_get($embed_url, array(
            'timeout' => 30,
            'headers' => array(
                'User-Agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            )
        ));

        if (is_wp_error($response)) {
            throw new Exception('Failed to get embed page: ' . $response->get_error_message());
        }

        $body = wp_remote_retrieve_body($response);

        // Look for download URLs in the embed page
        $download_patterns = array(
            '/download\.aspx\?[^"\']+/',
            '/guestaccess\.aspx\?[^"\']+/',
            '/_layouts\/15\/[^"\']*download[^"\']*/',
        );

        foreach ($download_patterns as $pattern) {
            if (preg_match($pattern, $body, $matches)) {
                $potential_url = $matches[0];

                // Make sure it's a complete URL
                if (!preg_match('/^https?:\/\//', $potential_url)) {
                    $url_parts = parse_url($this->onedrive_url);
                    $potential_url = $url_parts['scheme'] . '://' . $url_parts['host'] . '/' . ltrim($potential_url, '/');
                }

                $this->log('Found potential download URL: ' . $potential_url);

                if ($this->test_download_url($potential_url)) {
                    $this->log('Success with extracted URL: ' . $potential_url);
                    return $this->download_and_parse_from_url($potential_url);
                }
            }
        }

        throw new Exception('Could not extract download URL from embed page');
    }

    /**
     * Download method 3: Direct SharePoint access
     */
    private function download_method_direct_sharepoint() {
        $this->log('Trying direct SharePoint API access...');

        // Extract the file path from the OneDrive URL
        $url_parts = parse_url($this->onedrive_url);
        $path = $url_parts['path'];

        // Convert to API endpoint
        if (preg_match('/\/personal\/([^\/]+)\/Documents\/(.+)/', $path, $matches)) {
            $user_path = $matches[1];
            $file_path = $matches[2];

            $api_url = $url_parts['scheme'] . '://' . $url_parts['host'] .
                      '/personal/' . $user_path . '/_api/web/GetFileByServerRelativeUrl(\'/personal/' .
                      $user_path . '/Documents/' . $file_path . '\')/\$value';

            $this->log('Constructed API URL: ' . $api_url);
            return $this->download_and_parse_from_url($api_url);
        }

        throw new Exception('Could not construct SharePoint API URL from OneDrive link');
    }

    /**
     * Download method 3: Embed method
     */
    private function download_method_embed_method() {
        $this->log('Trying embed method...');

        // Try to get the file through the embed URL
        $embed_url = str_replace('/:x:/', '/_layouts/15/Doc.aspx?sourcedoc=', $this->onedrive_url);
        $embed_url .= '&action=embedview&wdDownloadButton=True';

        $this->log('Embed URL: ' . $embed_url);
        return $this->download_and_parse_from_url($embed_url);
    }

    /**
     * Download method 4: CSV Export method
     */
    private function download_method_csv_export() {
        $this->log('Trying CSV export method...');

        // Try to convert to CSV export URL
        // This method assumes the user can provide a CSV export link
        $csv_url = str_replace('/:x:/', '/_layouts/15/Doc.aspx?sourcedoc=', $this->onedrive_url);
        $csv_url .= '&action=default&DefaultItemOpen=1&e=' . $this->extract_e_parameter($this->onedrive_url);

        // Alternative: try to construct a CSV download URL
        if (preg_match('/\/([a-zA-Z0-9_-]+)\?e=/', $this->onedrive_url, $matches)) {
            $file_id = $matches[1];
            $url_parts = parse_url($this->onedrive_url);
            $base_url = $url_parts['scheme'] . '://' . $url_parts['host'];

            // Try CSV export URL format
            $csv_export_url = $base_url . '/_layouts/15/download.aspx?docid=' . $file_id . '&authkey=' . $this->extract_auth_key($this->onedrive_url) . '&e=' . $this->extract_e_parameter($this->onedrive_url);

            $this->log('CSV export URL: ' . $csv_export_url);
            return $this->download_and_parse_from_url($csv_export_url);
        }

        throw new Exception('Could not construct CSV export URL');
    }

    /**
     * Extract e parameter from OneDrive URL
     */
    private function extract_e_parameter($url) {
        if (preg_match('/[?&]e=([^&]+)/', $url, $matches)) {
            return $matches[1];
        }
        return '';
    }

    /**
     * Download and parse from URL
     */
    private function download_and_parse_from_url($url) {
        $this->log('Attempting to download from URL: ' . $url);

        $response = wp_remote_get($url, array(
            'timeout' => 120, // Increased timeout
            'redirection' => 10, // Follow up to 10 redirects
            'headers' => array(
                'User-Agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'Accept' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/vnd.ms-excel,text/csv,*/*'
            )
        ));

        if (is_wp_error($response)) {
            $error_msg = 'Failed to download file: ' . $response->get_error_message();
            $this->log('ERROR: ' . $error_msg);
            throw new Exception($error_msg);
        }

        $response_code = wp_remote_retrieve_response_code($response);
        $this->log('HTTP Response Code: ' . $response_code);

        if ($response_code !== 200) {
            $error_msg = "HTTP error {$response_code} when downloading file";
            $this->log('ERROR: ' . $error_msg);

            // Log response headers for debugging
            $headers = wp_remote_retrieve_headers($response);
            $this->log('Response headers: ' . print_r($headers, true));

            throw new Exception($error_msg);
        }

        $body = wp_remote_retrieve_body($response);
        $body_size = strlen($body);
        $this->log('Downloaded file size: ' . $body_size . ' bytes');

        if (empty($body)) {
            throw new Exception('Downloaded file is empty');
        }

        // Check if the response looks like HTML (error page) instead of Excel/CSV
        if ($body_size < 1000 && (strpos($body, '<html') !== false || strpos($body, '<!DOCTYPE') !== false)) {
            $this->log('ERROR: Downloaded content appears to be HTML error page');
            $this->log('Content preview: ' . substr($body, 0, 500));
            throw new Exception('Downloaded content appears to be an error page, not the Excel file');
        }

        // Save temporary file
        $temp_file = wp_upload_dir()['basedir'] . '/temp_onedrive_sync_' . time() . '.xlsx';
        $bytes_written = file_put_contents($temp_file, $body);
        $this->log('Saved temporary file: ' . $temp_file . ' (' . $bytes_written . ' bytes)');

        // Parse Excel file
        $this->log('Attempting to parse file...');

        try {
            $excel_data = $this->parse_excel_file($temp_file);

            if (!$excel_data || empty($excel_data)) {
                $this->log('ERROR: Failed to parse file or file is empty');

                // Try to get more info about the file
                $file_info = array(
                    'size' => filesize($temp_file),
                    'mime' => mime_content_type($temp_file),
                    'extension' => pathinfo($temp_file, PATHINFO_EXTENSION)
                );
                $this->log('File info: ' . json_encode($file_info));

                // Read first few bytes to check file format
                $handle = fopen($temp_file, 'rb');
                $first_bytes = fread($handle, 100);
                fclose($handle);
                $this->log('First 100 bytes (hex): ' . bin2hex($first_bytes));

                // Keep temp file for debugging
                $debug_file = wp_upload_dir()['basedir'] . '/debug_onedrive_file_' . time() . '.xlsx';
                copy($temp_file, $debug_file);
                $this->log('Debug file saved: ' . $debug_file);

                throw new Exception('Failed to parse downloaded file - check debug file: ' . basename($debug_file));
            }

            $this->log('Successfully parsed ' . count($excel_data) . ' rows from file');

            // Clean up temp file
            unlink($temp_file);

        } catch (Exception $e) {
            $this->log('Parse error: ' . $e->getMessage());
            // Keep temp file for debugging
            $debug_file = wp_upload_dir()['basedir'] . '/debug_onedrive_file_' . time() . '.xlsx';
            copy($temp_file, $debug_file);
            unlink($temp_file);
            $this->log('Debug file saved: ' . $debug_file);
            throw $e;
        }

        return $excel_data;
    }

    /**
     * Convert OneDrive sharing URL to direct download URL
     */
    private function convert_onedrive_url($sharing_url) {
        $this->log('Converting OneDrive URL: ' . $sharing_url);

        // Parse the URL to extract components
        $url_parts = parse_url($sharing_url);
        $base_url = $url_parts['scheme'] . '://' . $url_parts['host'];

        // Method 1: For the specific OneDrive URL format provided
        // https://solitcon-my.sharepoint.com/:x:/g/personal/sander_martens_rewindrecords_nl/Ea6t_vytNjlMjxboM4Nta4YBYu61O_uVCLlXvP-iR4oH1A?e=fX2iUZ
        if (strpos($sharing_url, 'sharepoint.com') !== false && strpos($sharing_url, ':x:') !== false) {

            // Extract the file ID (the long string before ?e=)
            if (preg_match('/\/([a-zA-Z0-9_-]{20,})\?e=/', $sharing_url, $matches)) {
                $file_id = $matches[1];
                $this->log('Extracted file ID: ' . $file_id);

                // Extract the e parameter
                $e_param = $this->extract_e_parameter($sharing_url);
                $this->log('E parameter: ' . $e_param);

                // Try different download URL formats specific to this OneDrive structure
                $download_urls = array(
                    // Method 1a: Direct download with the file ID
                    $base_url . '/_layouts/15/download.aspx?share=' . $file_id . '&e=' . $e_param,

                    // Method 1b: Guest access with file ID
                    $base_url . '/_layouts/15/guestaccess.aspx?share=' . $file_id . '&e=' . $e_param,

                    // Method 1c: Download with UniqueId
                    $base_url . '/_layouts/15/download.aspx?UniqueId=' . $file_id,

                    // Method 1d: Replace :x: with download endpoint
                    str_replace('/:x:/', '/_layouts/15/download.aspx?share=', $sharing_url),

                    // Method 1e: WopiFrame download
                    str_replace('/:x:/', '/_layouts/15/WopiFrame.aspx?sourcedoc=', $sharing_url) . '&action=interactivepreview',

                    // Method 1f: Doc.aspx with download action
                    str_replace('/:x:/', '/_layouts/15/Doc.aspx?sourcedoc=', $sharing_url) . '&action=default'
                );

                foreach ($download_urls as $url) {
                    $this->log('Testing download URL: ' . $url);

                    if ($this->test_download_url($url)) {
                        $this->log('Success with URL: ' . $url);
                        return $url;
                    }
                }
            }
        }

        // Method 2: Try the original URL with download parameters
        $test_urls = array(
            // Add download parameter to original URL
            $sharing_url . '&download=1',
            str_replace('?e=', '&download=1&e=', $sharing_url),

            // Try with different action parameters
            str_replace('?e=', '&action=download&e=', $sharing_url),
            str_replace('?e=', '&action=embedview&wdDownloadButton=True&e=', $sharing_url)
        );

        foreach ($test_urls as $url) {
            $this->log('Testing modified URL: ' . $url);
            if ($this->test_download_url($url)) {
                $this->log('Success with modified URL: ' . $url);
                return $url;
            }
        }

        // Method 3: Try to construct API URLs
        if (preg_match('/\/personal\/([^\/]+)\//', $sharing_url, $matches)) {
            $personal_path = $matches[1];
            $this->log('Personal path: ' . $personal_path);

            $api_urls = array(
                $base_url . '/personal/' . $personal_path . '/_api/web/GetFileByServerRelativeUrl(\'/personal/' . $personal_path . '/Documents/shared_file\')/\$value',
                $base_url . '/personal/' . $personal_path . '/_layouts/15/download.aspx'
            );

            foreach ($api_urls as $url) {
                $this->log('Testing API URL: ' . $url);
                if ($this->test_download_url($url)) {
                    $this->log('Success with API URL: ' . $url);
                    return $url;
                }
            }
        }

        $this->log('All URL conversion methods failed');
        return false;
    }

    /**
     * Extract auth key from OneDrive URL
     */
    private function extract_auth_key($url) {
        if (preg_match('/authkey=([^&]+)/', $url, $matches)) {
            return $matches[1];
        }
        return '';
    }

    /**
     * Test if URL is accessible for download
     */
    private function test_download_url($url) {
        $response = wp_remote_head($url, array(
            'timeout' => 15,
            'redirection' => 5, // Follow redirects
            'headers' => array(
                'User-Agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'Accept' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/vnd.ms-excel,text/csv,*/*'
            )
        ));

        if (is_wp_error($response)) {
            $this->log('Test failed - Error: ' . $response->get_error_message());
            return false;
        }

        $response_code = wp_remote_retrieve_response_code($response);
        $this->log('Test response code: ' . $response_code);

        // Accept 200 (OK), 206 (partial content), and 302 (redirect) as valid responses
        if (in_array($response_code, array(200, 206, 302))) {
            $headers = wp_remote_retrieve_headers($response);
            $content_type = isset($headers['content-type']) ? $headers['content-type'] : '';

            $this->log('Content type: ' . $content_type);

            // For redirects, try to follow them manually to check final destination
            if ($response_code === 302) {
                $location = isset($headers['location']) ? $headers['location'] : '';
                if ($location) {
                    $this->log('Following redirect to: ' . $location);

                    // Test the redirect destination
                    $redirect_response = wp_remote_head($location, array(
                        'timeout' => 10,
                        'headers' => array(
                            'User-Agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                        )
                    ));

                    if (!is_wp_error($redirect_response)) {
                        $redirect_code = wp_remote_retrieve_response_code($redirect_response);
                        $redirect_headers = wp_remote_retrieve_headers($redirect_response);
                        $redirect_content_type = isset($redirect_headers['content-type']) ? $redirect_headers['content-type'] : '';

                        $this->log('Redirect response code: ' . $redirect_code);
                        $this->log('Redirect content type: ' . $redirect_content_type);

                        // Check if redirect leads to a valid file
                        if (in_array($redirect_code, array(200, 206))) {
                            // Check if it's not an HTML error page
                            if (strpos($redirect_content_type, 'text/html') === false ||
                                strpos($redirect_content_type, 'application/') !== false ||
                                strpos($redirect_content_type, 'text/csv') !== false) {
                                return true;
                            }
                        }
                    }
                }

                // If we can't follow the redirect properly, still consider 302 as potentially valid
                return true;
            }

            // For direct responses, check content type
            if (strpos($content_type, 'text/html') !== false &&
                strpos($content_type, 'application/') === false &&
                strpos($content_type, 'text/csv') === false) {
                $this->log('Test failed - Content type indicates HTML error page');
                return false;
            }

            return true;
        }

        $this->log('Test failed - Invalid response code: ' . $response_code);
        return false;
    }

    /**
     * Legacy test function for backward compatibility
     */
    private function test_url($url) {
        return $this->test_download_url($url);
    }

    /**
     * Parse Excel file
     */
    private function parse_excel_file($file_path) {
        $this->log('Parsing file: ' . $file_path);
        $csv_data = array();

        // Check file size and type
        $file_size = filesize($file_path);
        $this->log('File size: ' . $file_size . ' bytes');

        if ($file_size === 0) {
            throw new Exception('Downloaded file is empty');
        }

        // Check if file is actually an Excel file or CSV
        $file_extension = strtolower(pathinfo($file_path, PATHINFO_EXTENSION));
        $this->log('File extension: ' . $file_extension);

        // Try to detect actual file type by reading file header
        $handle = fopen($file_path, 'rb');
        $header = fread($handle, 8);
        fclose($handle);

        $this->log('File header (hex): ' . bin2hex($header));

        // Check for Excel file signatures
        $is_excel = false;
        if (substr($header, 0, 2) === 'PK') { // ZIP-based format (xlsx)
            $is_excel = true;
            $this->log('Detected XLSX format (ZIP-based)');
        } elseif (substr($header, 0, 8) === "\xD0\xCF\x11\xE0\xA1\xB1\x1A\xE1") { // OLE format (xls)
            $is_excel = true;
            $this->log('Detected XLS format (OLE-based)');
        } else {
            $this->log('File does not appear to be Excel format, trying as CSV');
        }

        if ($is_excel || $file_extension === 'xlsx' || $file_extension === 'xls') {
            // Try to convert Excel to CSV using a simple method
            $csv_data = $this->convert_excel_to_array($file_path);
        } else {
            // Try to read as CSV
            $csv_data = $this->parse_csv_file($file_path);
        }

        $this->log('Parsed ' . count($csv_data) . ' rows from file');
        return $csv_data;
    }

    /**
     * Parse CSV file
     */
    private function parse_csv_file($file_path) {
        $csv_data = array();

        // Try different delimiters (semicolon first for your CSV format)
        $delimiters = array(';', ',', '\t');

        foreach ($delimiters as $delimiter) {
            if (($handle = fopen($file_path, 'r')) !== FALSE) {
                $first_line = fgetcsv($handle, 1000, $delimiter);

                if ($first_line && count($first_line) >= 4) {
                    // Reset file pointer
                    rewind($handle);

                    // Skip header if it looks like a header
                    $header = fgetcsv($handle, 1000, $delimiter);
                    if ($this->looks_like_header($header)) {
                        // Skip header row
                    } else {
                        // Reset and include first row
                        rewind($handle);
                    }

                    while (($data = fgetcsv($handle, 1000, $delimiter)) !== FALSE) {
                        if (count($data) >= 4 && !empty(trim($data[2]))) { // Must have EAN
                            $csv_data[] = $data;
                        }
                    }
                    fclose($handle);

                    if (!empty($csv_data)) {
                        break; // Found valid data with this delimiter
                    }
                }
                fclose($handle);
            }
        }

        return $csv_data;
    }

    /**
     * Check if row looks like a header
     */
    private function looks_like_header($row) {
        if (!$row || count($row) < 4) {
            return false;
        }

        $header_indicators = array('titel', 'title', 'artiest', 'artist', 'ean', 'barcode', 'voorraad', 'stock', 'inventory');

        foreach ($row as $cell) {
            $cell_lower = strtolower(trim($cell));
            if (in_array($cell_lower, $header_indicators)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Convert Excel to array (basic implementation)
     */
    private function convert_excel_to_array($file_path) {
        $this->log('Converting Excel file to array...');

        // Method 1: Try to use PHP's built-in ZIP functions for XLSX files
        if (class_exists('ZipArchive')) {
            $result = $this->parse_xlsx_with_zip($file_path);
            if ($result && !empty($result)) {
                $this->log('Successfully parsed XLSX using ZIP method');
                return $result;
            }
        }

        // Method 2: Try to read as CSV (in case OneDrive converted it)
        $this->log('Trying to read as CSV...');
        $csv_result = $this->parse_csv_file($file_path);
        if ($csv_result && !empty($csv_result)) {
            $this->log('Successfully parsed as CSV');
            return $csv_result;
        }

        // Method 3: Try to extract content and look for CSV-like data
        $content = file_get_contents($file_path);
        $this->log('File content length: ' . strlen($content));

        // Check if it contains CSV-like content
        if (strpos($content, ',') !== false || strpos($content, ';') !== false || strpos($content, "\t") !== false) {
            $this->log('Found delimiter characters, trying as CSV content...');

            // Save as temporary CSV and parse
            $temp_csv = wp_upload_dir()['basedir'] . '/temp_excel_as_csv_' . time() . '.csv';
            file_put_contents($temp_csv, $content);
            $result = $this->parse_csv_file($temp_csv);
            unlink($temp_csv);

            if ($result && !empty($result)) {
                $this->log('Successfully parsed content as CSV');
                return $result;
            }
        }

        // If all methods fail, provide helpful error message
        $this->log('ERROR: Could not parse Excel file. File may be in unsupported format.');
        $this->log('Suggestion: Please save the Excel file as CSV format and use manual upload.');

        return array();
    }

    /**
     * Parse XLSX file using ZIP functions
     */
    private function parse_xlsx_with_zip($file_path) {
        $this->log('Attempting to parse XLSX using ZIP method...');

        try {
            $zip = new ZipArchive();
            $result = $zip->open($file_path);

            if ($result !== TRUE) {
                $this->log('Failed to open XLSX as ZIP: ' . $result);
                return false;
            }

            // Look for the main worksheet
            $worksheet_xml = $zip->getFromName('xl/worksheets/sheet1.xml');
            $shared_strings_xml = $zip->getFromName('xl/sharedStrings.xml');

            $zip->close();

            if ($worksheet_xml === false) {
                $this->log('Could not find worksheet in XLSX file');
                return false;
            }

            // Parse shared strings if available
            $shared_strings = array();
            if ($shared_strings_xml !== false) {
                $shared_strings = $this->parse_shared_strings($shared_strings_xml);
                $this->log('Found ' . count($shared_strings) . ' shared strings');
            }

            // Parse worksheet
            $rows = $this->parse_worksheet($worksheet_xml, $shared_strings);
            $this->log('Extracted ' . count($rows) . ' rows from XLSX');

            return $rows;

        } catch (Exception $e) {
            $this->log('Error parsing XLSX: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Parse shared strings XML
     */
    private function parse_shared_strings($xml_content) {
        $strings = array();

        if (preg_match_all('/<t[^>]*>(.*?)<\/t>/s', $xml_content, $matches)) {
            foreach ($matches[1] as $string) {
                $strings[] = html_entity_decode($string, ENT_XML1, 'UTF-8');
            }
        }

        return $strings;
    }

    /**
     * Parse worksheet XML
     */
    private function parse_worksheet($xml_content, $shared_strings = array()) {
        $rows = array();

        // Extract all row data
        if (preg_match_all('/<row[^>]*>(.*?)<\/row>/s', $xml_content, $row_matches)) {
            foreach ($row_matches[1] as $row_xml) {
                $row_data = array();

                // Extract cells from this row
                if (preg_match_all('/<c[^>]*r="([A-Z]+\d+)"[^>]*(?:\st="([^"]*)")?[^>]*>(.*?)<\/c>/s', $row_xml, $cell_matches)) {
                    for ($i = 0; $i < count($cell_matches[0]); $i++) {
                        $cell_ref = $cell_matches[1][$i];
                        $cell_type = $cell_matches[2][$i];
                        $cell_content = $cell_matches[3][$i];

                        // Extract column index from cell reference (A=0, B=1, etc.)
                        $col_index = $this->column_index_from_string(preg_replace('/\d+/', '', $cell_ref));

                        // Get cell value
                        if (preg_match('/<v[^>]*>(.*?)<\/v>/', $cell_content, $value_match)) {
                            $value = $value_match[1];

                            // Handle shared strings
                            if ($cell_type === 's' && isset($shared_strings[$value])) {
                                $value = $shared_strings[$value];
                            }

                            // Ensure array is large enough
                            while (count($row_data) <= $col_index) {
                                $row_data[] = '';
                            }

                            $row_data[$col_index] = $value;
                        }
                    }
                }

                if (!empty($row_data)) {
                    $rows[] = $row_data;
                }
            }
        }

        return $rows;
    }

    /**
     * Convert column letter to index (A=0, B=1, etc.)
     */
    private function column_index_from_string($column) {
        $index = 0;
        $length = strlen($column);

        for ($i = 0; $i < $length; $i++) {
            $index = $index * 26 + (ord($column[$i]) - ord('A') + 1);
        }

        return $index - 1;
    }

    /**
     * Process Excel data and update WordPress records
     */
    private function process_excel_data($excel_data) {
        $updated = 0;
        $added = 0;
        $zeroed = 0;
        $excel_eans = array();

        // Detect column structure from first few rows
        $column_mapping = $this->detect_column_mapping($excel_data);

        if (!$column_mapping) {
            throw new Exception('Could not detect column structure in Excel file');
        }

        // Skip header row if detected
        $start_row = 0;
        if (isset($column_mapping['header_row_index'])) {
            $start_row = $column_mapping['header_row_index'] + 1;
            unset($column_mapping['header_row_index']); // Remove this from mapping
            $this->log("Starting data processing from row {$start_row} (skipping header)");
        }

        foreach ($excel_data as $row_index => $row) {
            // Skip header rows and empty rows
            if ($row_index < $start_row) {
                continue;
            }
            // Skip if not enough columns
            if (count($row) < 11) {
                $this->log("Skipping row " . ($row_index + 1) . ": not enough columns (" . count($row) . ")");
                continue;
            }

            $title = isset($row[$column_mapping['title']]) ? trim($row[$column_mapping['title']]) : '';
            $artist = isset($row[$column_mapping['artist']]) ? trim($row[$column_mapping['artist']]) : '';
            $ean = isset($row[$column_mapping['ean']]) ? trim($row[$column_mapping['ean']]) : '';
            $stock = isset($row[$column_mapping['stock']]) ? intval($row[$column_mapping['stock']]) : 0;
            $article_number = isset($row[$column_mapping['article_number']]) ? trim($row[$column_mapping['article_number']]) : '';

            // If no separate artist column, try to extract artist from title
            if (empty($artist) && !empty($title)) {
                $artist = $this->extract_artist_from_title($title);
            }

            // Skip rows without essential data or header-like rows
            if (empty($ean) || empty($title) ||
                stripos($title, 'omschrijving') !== false ||
                stripos($ean, 'barcode') !== false ||
                !is_numeric($ean)) {
                $this->log("Skipping row " . ($row_index + 1) . ": missing/invalid EAN or title (EAN: '{$ean}', Title: '{$title}')");
                continue;
            }

            // Additional validation for EAN
            if (strlen($ean) < 8 || strlen($ean) > 14) {
                $this->log("Skipping row " . ($row_index + 1) . ": invalid EAN length (EAN: '{$ean}')");
                continue;
            }

            $excel_eans[] = $ean;

            // Check if record exists by EAN
            $existing_posts = $this->get_records_by_ean($ean);

            if (count($existing_posts) > 1) {
                // Handle duplicates: merge stock and keep only one
                $this->handle_duplicate_records($existing_posts, $stock);
                $updated++;
            } elseif (count($existing_posts) === 1) {
                // Update existing record
                $post_id = $existing_posts[0];
                update_post_meta($post_id, '_record_stock', $stock);
                $this->log("Updated stock for record ID {$post_id} (EAN: {$ean}) to {$stock}");
                $updated++;
            } else {
                // Create new record
                $post_id = $this->create_new_record($title, $artist, $ean, $stock, $article_number);
                if ($post_id) {
                    $this->log("Created new record ID {$post_id} (EAN: {$ean}) with stock {$stock}");
                    $added++;
                }
            }
        }

        // Set stock to 0 for records not in Excel file
        $zeroed = $this->zero_missing_records($excel_eans);

        return array(
            'updated' => $updated,
            'added' => $added,
            'zeroed' => $zeroed
        );
    }

    /**
     * Get records by EAN code
     */
    private function get_records_by_ean($ean) {
        $posts = get_posts(array(
            'post_type' => 'record',
            'posts_per_page' => -1,
            'meta_query' => array(
                array(
                    'key' => '_record_ean_code',
                    'value' => $ean,
                    'compare' => '='
                )
            ),
            'fields' => 'ids'
        ));

        return $posts;
    }

    /**
     * Handle duplicate records with same EAN
     */
    private function handle_duplicate_records($post_ids, $new_stock) {
        if (count($post_ids) <= 1) {
            return;
        }

        // Keep the first post, delete the others
        $keep_post_id = $post_ids[0];
        $total_stock = $new_stock;

        // Get existing stock from posts to be deleted and add to total
        for ($i = 1; $i < count($post_ids); $i++) {
            $existing_stock = get_post_meta($post_ids[$i], '_record_stock', true);
            if ($existing_stock) {
                $total_stock += intval($existing_stock);
            }

            // Delete the duplicate post
            wp_delete_post($post_ids[$i], true);
            $this->log("Deleted duplicate record ID {$post_ids[$i]}");
        }

        // Update the kept post with combined stock
        update_post_meta($keep_post_id, '_record_stock', $total_stock);
        $this->log("Updated record ID {$keep_post_id} with combined stock: {$total_stock}");
    }

    /**
     * Create new record
     */
    private function create_new_record($title, $artist, $ean, $stock, $article_number = '') {
        $post_data = array(
            'post_title' => $title,
            'post_type' => 'record',
            'post_status' => 'publish',
            'post_content' => ''
        );

        $post_id = wp_insert_post($post_data);

        if ($post_id && !is_wp_error($post_id)) {
            // Set meta fields
            update_post_meta($post_id, '_record_ean_code', $ean);
            update_post_meta($post_id, '_record_stock', $stock);

            if (!empty($article_number)) {
                update_post_meta($post_id, '_record_article_id', $article_number);
            }

            // Set artist taxonomy
            if (!empty($artist)) {
                wp_set_object_terms($post_id, $artist, 'record_artist');
            }

            // Set default product type to album
            wp_set_object_terms($post_id, 'album', 'record_product_type');

            return $post_id;
        }

        return false;
    }

    /**
     * Extract artist from title if possible
     */
    private function extract_artist_from_title($title) {
        // Common patterns for artist - title separation
        $patterns = array(
            '/^(.+?)\s*-\s*(.+)$/',  // Artist - Title
            '/^(.+?)\s*:\s*(.+)$/',  // Artist : Title
            '/^(.+?)\s*–\s*(.+)$/',  // Artist – Title (em dash)
            '/^(.+?)\s*\|\s*(.+)$/', // Artist | Title
        );

        foreach ($patterns as $pattern) {
            if (preg_match($pattern, $title, $matches)) {
                $potential_artist = trim($matches[1]);
                $potential_title = trim($matches[2]);

                // Basic validation: artist shouldn't be too long or contain certain words
                if (strlen($potential_artist) <= 100 &&
                    !preg_match('/\b(vinyl|lp|cd|album|record|disc)\b/i', $potential_artist)) {
                    return $potential_artist;
                }
            }
        }

        // If no pattern matches, return empty string
        return '';
    }

    /**
     * Set stock to 0 for records not in Excel file
     */
    private function zero_missing_records($excel_eans) {
        $zeroed = 0;

        // Get all records with EAN codes
        $all_records = get_posts(array(
            'post_type' => 'record',
            'posts_per_page' => -1,
            'meta_query' => array(
                array(
                    'key' => '_record_ean_code',
                    'value' => '',
                    'compare' => '!='
                )
            ),
            'fields' => 'ids'
        ));

        foreach ($all_records as $post_id) {
            $ean = get_post_meta($post_id, '_record_ean_code', true);

            if (!in_array($ean, $excel_eans)) {
                $current_stock = get_post_meta($post_id, '_record_stock', true);

                if ($current_stock > 0) {
                    update_post_meta($post_id, '_record_stock', 0);
                    $this->log("Set stock to 0 for record ID {$post_id} (EAN: {$ean}) - not found in Excel");
                    $zeroed++;
                }
            }
        }

        return $zeroed;
    }

    /**
     * Detect column mapping from Excel data
     */
    private function detect_column_mapping($excel_data) {
        if (empty($excel_data)) {
            $this->log('ERROR: Excel data is empty');
            return false;
        }

        $this->log('Detecting column mapping from ' . count($excel_data) . ' rows');

        // Log first few rows for debugging
        for ($i = 0; $i < min(5, count($excel_data)); $i++) {
            $row_preview = array_slice($excel_data[$i], 0, 15); // First 15 columns
            $this->log("Row {$i} (" . count($excel_data[$i]) . " columns): " . json_encode($row_preview));
        }

        // Also log some middle rows to see actual data
        if (count($excel_data) > 10) {
            for ($i = 5; $i < min(10, count($excel_data)); $i++) {
                $row_preview = array_slice($excel_data[$i], 0, 15);
                $this->log("Data Row {$i}: " . json_encode($row_preview));
            }
        }

        // Check first few rows to find the best column mapping
        $sample_rows = array_slice($excel_data, 0, min(10, count($excel_data)));

        // Define possible column names for each field (based on your CSV structure)
        $field_patterns = array(
            'title' => array('omschrijving', 'titel', 'title', 'naam', 'name', 'album', 'description', 'product'),
            'artist' => array('artiest', 'artist', 'kunstenaar', 'performer', 'band', 'groep', 'group'),
            'ean' => array('barcode', 'ean', 'ean13', 'ean-13', 'code', 'artikelcode', 'product_code'),
            'stock' => array('voorraad', 'stock', 'inventory', 'aantal', 'quantity', 'qty', 'hoeveelheid'),
            'article_number' => array('artikelnummer', 'article_number', 'artikel_nummer')
        );

        $mapping = array();

        // Try to find columns by header names first
        $first_row = $excel_data[0];
        $this->log('First row (potential headers): ' . json_encode($first_row));

        foreach ($first_row as $col_index => $header) {
            if (empty($header)) continue;

            $header_lower = strtolower(trim($header));
            $this->log("Checking header '{$header}' (column {$col_index})");

            foreach ($field_patterns as $field => $patterns) {
                foreach ($patterns as $pattern) {
                    if (strpos($header_lower, $pattern) !== false) {
                        $mapping[$field] = $col_index;
                        $this->log("Mapped '{$field}' to column {$col_index} (header: '{$header}')");
                        break 2;
                    }
                }
            }
        }

        // If we found at least 3 mappings, consider it a header row
        if (count($mapping) >= 3) {
            $this->log('Detected column headers: ' . json_encode($mapping));
            return $mapping;
        }

        // Special case: Check if this looks like your specific CSV format
        // Look for the header row pattern or data pattern
        $header_row_found = false;
        $header_row_index = -1;

        // Check first few rows for header pattern
        for ($i = 0; $i < min(3, count($excel_data)); $i++) {
            $row = $excel_data[$i];
            if (count($row) >= 11 &&
                (stripos($row[0], 'artikel') !== false || stripos($row[0], 'nummer') !== false) &&
                (stripos($row[1], 'barcode') !== false) &&
                (stripos($row[5], 'omschrijving') !== false) &&
                (stripos($row[10], 'voorraad') !== false)) {

                $this->log("Found header row at index {$i}");
                $header_row_found = true;
                $header_row_index = $i;
                break;
            }
        }

        // If header found, use the known structure
        if ($header_row_found) {
            $this->log('Detected specific CSV format with known structure');
            return array(
                'article_number' => 0,
                'ean' => 1,
                'title' => 5,
                'stock' => 10,
                'header_row_index' => $header_row_index
            );
        }

        // Alternative: Check if data pattern matches your CSV structure
        // Look for rows with numeric article numbers, barcodes, and descriptions
        $data_pattern_matches = 0;
        for ($i = 0; $i < min(10, count($excel_data)); $i++) {
            $row = $excel_data[$i];
            if (count($row) >= 11) {
                // Check if column 0 looks like article number (numeric)
                // Check if column 1 looks like barcode (numeric, 8+ digits)
                // Check if column 5 has text description
                // Check if column 10 is numeric (stock)

                $col0_numeric = is_numeric(trim($row[0]));
                $col1_barcode = is_numeric(trim($row[1])) && strlen(trim($row[1])) >= 8;
                $col5_text = !empty(trim($row[5])) && !is_numeric(trim($row[5]));
                $col10_numeric = is_numeric(trim($row[10])) || empty(trim($row[10]));

                if ($col0_numeric && $col1_barcode && $col5_text && $col10_numeric) {
                    $data_pattern_matches++;
                }
            }
        }

        if ($data_pattern_matches >= 3) {
            $this->log("Detected CSV data pattern (matches: {$data_pattern_matches})");
            return array(
                'article_number' => 0,
                'ean' => 1,
                'title' => 5,
                'stock' => 10
            );
        }

        $this->log('No clear headers found, trying data pattern detection...');

        // Hardcoded fallback for your specific Excel structure
        // Based on your CSV example: ArtikelNummer;Barcode;...;Omschrijving;...;Voorraad
        if (count($excel_data) > 0 && count($excel_data[0]) >= 11) {
            $this->log('Trying hardcoded mapping for your Excel structure (0,1,5,10)');

            // Test this mapping with a few rows
            $test_rows = array_slice($excel_data, 1, 5); // Skip potential header
            $valid_rows = 0;

            foreach ($test_rows as $row) {
                if (count($row) >= 11) {
                    $artikel_nr = trim($row[0]);
                    $barcode = trim($row[1]);
                    $omschrijving = trim($row[5]);
                    $voorraad = trim($row[10]);

                    // Check if this looks like valid data
                    if (is_numeric($artikel_nr) &&
                        (is_numeric($barcode) || empty($barcode)) &&
                        !empty($omschrijving) &&
                        (is_numeric($voorraad) || empty($voorraad))) {
                        $valid_rows++;
                    }
                }
            }

            if ($valid_rows >= 2) {
                $this->log("Hardcoded mapping validated with {$valid_rows} valid rows");
                return array(
                    'article_number' => 0,
                    'ean' => 1,
                    'title' => 5,
                    'stock' => 10
                );
            }
        }

        // Try to detect by analyzing data patterns in multiple rows
        $possible_mappings = array(
            array('title' => 0, 'artist' => 1, 'ean' => 2, 'stock' => 3),
            array('title' => 1, 'artist' => 0, 'ean' => 2, 'stock' => 3),
            array('title' => 0, 'artist' => 1, 'ean' => 3, 'stock' => 2),
            array('title' => 1, 'artist' => 2, 'ean' => 0, 'stock' => 3),
            // Add more possible combinations based on common Excel layouts
            array('title' => 2, 'artist' => 1, 'ean' => 0, 'stock' => 3),
            array('title' => 0, 'artist' => 2, 'ean' => 1, 'stock' => 3),
        );

        foreach ($possible_mappings as $test_mapping) {
            $this->log('Testing mapping: ' . json_encode($test_mapping));
            $score = $this->score_column_mapping($sample_rows, $test_mapping);
            $this->log("Mapping score: {$score}");

            if ($score >= 0.7) { // 70% confidence threshold
                $this->log('Using detected column mapping: ' . json_encode($test_mapping));
                return $test_mapping;
            }
        }

        // If no good mapping found, try to find any columns with EAN-like data
        $this->log('Trying to find EAN column by data pattern...');
        for ($col = 0; $col < min(20, count($first_row)); $col++) {
            $ean_like_count = 0;
            $total_checked = 0;

            for ($row = 0; $row < min(10, count($sample_rows)); $row++) {
                if (isset($sample_rows[$row][$col])) {
                    $value = trim($sample_rows[$row][$col]);
                    if (!empty($value)) {
                        $total_checked++;
                        if (is_numeric($value) && strlen($value) >= 8 && strlen($value) <= 14) {
                            $ean_like_count++;
                        }
                    }
                }
            }

            if ($total_checked > 0 && ($ean_like_count / $total_checked) >= 0.8) {
                $this->log("Found potential EAN column at index {$col}");
                // Build a basic mapping around the EAN column
                $basic_mapping = array(
                    'title' => max(0, $col - 2),
                    'artist' => max(0, $col - 1),
                    'ean' => $col,
                    'stock' => min($col + 1, count($first_row) - 1)
                );
                $this->log('Using EAN-based mapping: ' . json_encode($basic_mapping));
                return $basic_mapping;
            }
        }

        $this->log('ERROR: Could not detect any valid column mapping');
        return false;
    }

    /**
     * Score a column mapping based on data patterns
     */
    private function score_column_mapping($sample_rows, $mapping) {
        $score = 0;
        $total_tests = 0;

        foreach ($sample_rows as $row) {
            if (count($row) <= max($mapping)) {
                continue; // Skip rows that don't have enough columns
            }

            // Test EAN column
            if (isset($mapping['ean']) && isset($row[$mapping['ean']])) {
                $ean_value = trim($row[$mapping['ean']]);
                $total_tests++;
                if (!empty($ean_value) && is_numeric($ean_value) && strlen($ean_value) >= 8 && strlen($ean_value) <= 14) {
                    $score++;
                }
            }

            // Test stock column
            if (isset($mapping['stock']) && isset($row[$mapping['stock']])) {
                $stock_value = trim($row[$mapping['stock']]);
                $total_tests++;
                if (empty($stock_value) || is_numeric($stock_value)) {
                    $score++;
                }
            }

            // Test title column (should not be numeric)
            if (isset($mapping['title']) && isset($row[$mapping['title']])) {
                $title_value = trim($row[$mapping['title']]);
                $total_tests++;
                if (!empty($title_value) && !is_numeric($title_value)) {
                    $score++;
                }
            }
        }

        return $total_tests > 0 ? $score / $total_tests : 0;
    }

    /**
     * Log message to file
     */
    private function log($message) {
        $timestamp = date('Y-m-d H:i:s');
        $log_entry = "[{$timestamp}] {$message}" . PHP_EOL;
        file_put_contents($this->log_file, $log_entry, FILE_APPEND | LOCK_EX);
    }
}
