<?php
/**
 * The template for displaying record artist archives
 */

get_header();

$current_term = get_queried_object();
?>

<section class="recordsArchive artistArchive">
    <div class="contentWrapper">
        <h1 class="bigTitle white"><?php echo esc_html($current_term->name); ?></h1>

        <?php if (!empty($current_term->description)) : ?>
            <div class="termDescription">
                <?php echo wpautop($current_term->description); ?>
            </div>
        <?php endif; ?>

        <div class="recordsGrid">
            <?php
            if (have_posts()) :
                while (have_posts()) : the_post();
                    rewindrecords_render_record_item(get_the_ID(), array(
                        'image_size' => 'record-cover-optimized',
                        'show_artist' => false, // Don't show artist on artist archive page
                        'show_year' => true,
                        'show_format' => true,
                        'show_price' => true,
                        'lazy_load' => false, // No lazy loading on initial page load
                    ));
                endwhile;
                ?>
                <div class="pagination">
                    <?php
                    echo paginate_links(array(
                        'prev_text' => '<i class="icon-arrow-left"></i> ' . __('Previous', 'rewindrecords'),
                        'next_text' => __('Next', 'rewindrecords') . ' <i class="icon-arrow-right"></i>',
                    ));
                    ?>
                </div>
            <?php else : ?>
                <div class="noRecords">
                    <p><?php _e('No records found for this artist.', 'rewindrecords'); ?></p>
                </div>
            <?php endif; ?>
        </div>
    </div>
</section>

<?php
get_footer();
