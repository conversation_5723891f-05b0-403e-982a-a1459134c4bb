/* Partners Block Styles */
.partnersBlock {
    padding: 60px 0;
}

.partnersBlock .biggerTitle {
    margin-bottom: 20px;
    text-align: center;
}

.partnersBlock .description {
    max-width: 800px;
    margin: 0 auto 40px;
    text-align: center;
}

.partnersBlock .partnersGrid {
    display: block;
    margin: 0 -15px;
    text-align: center;
}

.partnersBlock .partnerItem {
    display: inline-block;
    vertical-align: top;
    padding: 15px;
    margin-bottom: 30px;
    text-align: center;
}

.partnersBlock .col-1 .partnerItem {
    width: 100%;
}

.partnersBlock .col-2 .partnerItem {
    width: 50%;
}

.partnersBlock .col-3 .partnerItem {
    width: 33.333%;
}

.partnersBlock .col-4 .partnerItem {
    width: 25%;
}

.partnersBlock .col-5 .partnerItem {
    width: 20%;
}

.partnersBlock .col-6 .partnerItem {
    width: 16.666%;
}

.partnersBlock .partnerImageWrapper {
    margin-bottom: 15px;
    height: 120px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.partnersBlock .partnerImageWrapper img {
    max-width: 100%;
    max-height: 100%;
    width: auto;
    height: auto;
}

.partnersBlock .partnerName {
    font-weight: 500;
}

.partnersBlock a {
    text-decoration: none;
    color: inherit;
    display: block;
    height: 100%;
    transition: transform 0.3s ease;
}

.partnersBlock a:hover {
    transform: translateY(-5px);
}

/* Responsive styles */
@media (max-width: 1024px) {
    .partnersBlock .col-4 .partnerItem,
    .partnersBlock .col-5 .partnerItem,
    .partnersBlock .col-6 .partnerItem {
        width: 33.333%;
    }
}

@media (max-width: 768px) {
    .partnersBlock .col-3 .partnerItem,
    .partnersBlock .col-4 .partnerItem,
    .partnersBlock .col-5 .partnerItem,
    .partnersBlock .col-6 .partnerItem {
        width: 50%;
    }
}

@media (max-width: 480px) {
    .partnersBlock .partnerItem {
        width: 100% !important;
    }
}
