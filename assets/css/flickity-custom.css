/**
 * Custom Flickity button styles
 */

/* Hide SVG icons */
.flickity-button svg {
    display: none;
}

/* Style the icon font arrows */
.flickity-button i {
    font-size: 24px;
    line-height: 1;
    display: block;
}

/* Center the icons in the buttons */
.flickity-button {
    display: block;
    text-align: center;
    position: relative;
}

.flickity-button i {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

/* Ensure proper sizing on different screen sizes */
@media screen and (max-width: 768px) {
    .flickity-button i {
        font-size: 18px;
    }
}
