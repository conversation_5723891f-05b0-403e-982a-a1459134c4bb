/**
 * Custom Flickity buttons
 * Replace SVG arrows with icon font
 */
(function($) {
    'use strict';
    
    // Function to replace Flickity arrows with icon font
    function replaceFlickityArrows() {
        // Find all Flickity instances
        $('.flickity-enabled').each(function() {
            var $slider = $(this);
            
            // Check if arrows have already been replaced
            if ($slider.hasClass('arrows-replaced')) {
                return;
            }
            
            // Replace previous button SVG with icon
            var $prevButton = $slider.find('.flickity-button.previous');
            if ($prevButton.length && !$prevButton.find('.icon-arrow-left').length) {
                $prevButton.find('svg').remove();
                $prevButton.append('<i class="icon-arrow-left"></i>');
            }
            
            // Replace next button SVG with icon
            var $nextButton = $slider.find('.flickity-button.next');
            if ($nextButton.length && !$nextButton.find('.icon-arrow-right').length) {
                $nextButton.find('svg').remove();
                $nextButton.append('<i class="icon-arrow-right"></i>');
            }
            
            // Mark as replaced
            $slider.addClass('arrows-replaced');
        });
    }
    
    // Run on document ready
    $(document).ready(function() {
        // Initial replacement
        replaceFlickityArrows();
        
        // Replace arrows when new sliders are initialized
        $(document).on('initPage', function() {
            setTimeout(replaceFlickityArrows, 100);
        });
        
        // For AJAX-loaded content or dynamically created sliders
        $(document).on('flickity-ready', function() {
            setTimeout(replaceFlickityArrows, 100);
        });
        
        // Fallback for sliders that might be initialized after page load
        setInterval(replaceFlickityArrows, 1000);
    });
    
})(jQuery);
