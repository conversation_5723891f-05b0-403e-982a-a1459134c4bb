/**
 * Records Archive Filtering
 * Implements select2 for filter dropdowns and AJAX filtering
 */
var recordsArchiveData;
(function($) {
    'use strict';

    // Initialize on document ready
    $(document).ready(function() {
        // Bind to the initPage event for Swup compatibility
        $(document).on("initPage", function() {
            if ($('.recordsArchive').length > 0) {
                initRecordsArchiveFilters();
                initPaginationLinks();
                lazyLoadImages();
            }
        });
    });

    // Initialize pagination links to use AJAX
    function initPaginationLinks() {
        $('.pagination a').off('click.pagination').on('click.pagination', function(e) {
            e.preventDefault();

            var pageUrl = $(this).attr('href');
            var pageNum = getParameterByName('paged', pageUrl) || 1;

            // Get current filter values
            var genre = $('#genre-filter').val();
            var artist = $('#artist-filter').val();
            var sort = $('#sort-filter').val();

            // Build a new URL with all parameters
            var url = recordsArchiveData.archiveUrl;
            var queryParams = {};

            // Only add genre if it's not 'all'
            if (genre && genre !== 'all') {
                queryParams.record_genre = genre;
            }

            // Only add artist if it's not 'all'
            if (artist && artist !== 'all') {
                queryParams.record_artist = artist;
            }

            if (sort) {
                queryParams.sort = sort;
            }

            // Add the page number
            queryParams.paged = pageNum;

            // Build the URL with query parameters
            url = addQueryParams(url, queryParams);

            // Update browser URL without reloading the page
            window.history.pushState({}, '', url);

            // Show loading state
            $('.recordsGrid').addClass('loading');

            // Scroll to top of filters on mobile
            if (window.matchMedia('(max-width: 580px)').matches) {
                $('html, body').animate({
                    scrollTop: $('.recordFilters').offset().top - 20
                }, 300);
            }

            // Fetch filtered records via AJAX
            $.ajax({
                url: recordsArchiveData.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'filter_records',
                    genre: genre,
                    artist: artist,
                    sort: sort,
                    paged: pageNum,
                    nonce: recordsArchiveData.nonce
                },
                success: function(response) {
                    // Replace the records grid with the new content
                    $('.recordsGrid').html(response.data.html);
                    $('.pagination').html(response.data.pagination);

                    // Re-initialize pagination links
                    initPaginationLinks();

                    // Initialize lazy loading for new images
                    lazyLoadImages();

                    // Scroll to top of records grid, adjust for mobile
                    var scrollOffset = window.matchMedia('(max-width: 580px)').matches ? 20 : 100;
                    $('html, body').animate({
                        scrollTop: $('.recordsArchive').offset().top - scrollOffset
                    }, 500);

                    // Remove loading state
                    $('.recordsGrid').removeClass('loading');
                },
                error: function() {
                    // Remove loading state and show error message
                    $('.recordsGrid').removeClass('loading');
                    $('.recordsGrid').html('<div class="noRecords"><p>' + recordsArchiveData.errorMessage + '</p></div>');
                }
            });
        });
    }

    // Helper function to get URL parameters
    function getParameterByName(name, url) {
        if (!url) url = window.location.href;
        name = name.replace(/[\[\]]/g, '\\$&');
        var regex = new RegExp('[?&]' + name + '(=([^&#]*)|&|#|$)'),
            results = regex.exec(url);
        if (!results) return null;
        if (!results[2]) return '';
        return decodeURIComponent(results[2].replace(/\+/g, ' '));
    }

    // Helper function to add query parameters to a URL
    function addQueryParams(url, params) {
        var queryString = Object.keys(params)
            .filter(function(key) {
                return params[key] !== null && params[key] !== undefined && params[key] !== '';
            })
            .map(function(key) {
                return encodeURIComponent(key) + '=' + encodeURIComponent(params[key]);
            })
            .join('&');

        if (queryString) {
            // Check if URL already has parameters
            return url + (url.indexOf('?') === -1 ? '?' : '&') + queryString;
        }

        return url;
    }

    // Helper function to initialize lazy loading for images
    function lazyLoadImages() {
        // Find all lazy-loaded images
        var lazyImages = document.querySelectorAll('img.lazy');

        if (lazyImages.length > 0) {
            // Create an intersection observer
            var imageObserver = new IntersectionObserver(function(entries, observer) {
                entries.forEach(function(entry) {
                    // If the image is in the viewport
                    if (entry.isIntersecting) {
                        var image = entry.target;

                        // Set the src attribute to the data-src value
                        if (image.dataset.src) {
                            image.src = image.dataset.src;
                            image.removeAttribute('data-src');
                        }

                        // Remove the lazy class
                        image.classList.remove('lazy');

                        // Stop observing the image
                        observer.unobserve(image);
                    }
                });
            });

            // Observe each lazy image
            lazyImages.forEach(function(image) {
                imageObserver.observe(image);
            });
        }
    }

    // Initialize the filters
    function initRecordsArchiveFilters() {
        // Initialize select2 for all filter dropdowns
        $('.recordFilter').each(function() {
            // Check if select2 is already initialized
            if (!$(this).hasClass('select2-hidden-accessible')) {
                var isMobile = window.matchMedia('(max-width: 580px)').matches;
                var isTablet = window.matchMedia('(max-width: 1160px)').matches && !isMobile;

                var selectOptions = {
                    minimumResultsForSearch: isMobile ? 0 : 8, // Always show search on mobile
                    width: '100%',
                    dropdownCssClass: 'records-filter-dropdown',
                    placeholder: $(this).find('option:first').text(),
                    allowClear: true
                };

                // Special handling for genre and artist filters to ensure "All" options are always available
                if ($(this).attr('id') === 'genre-filter' || $(this).attr('id') === 'artist-filter') {
                    // Make sure the empty option is always selectable
                    $(this).find('option[value=""]').prop('disabled', false);
                }

                $(this).select2(selectOptions);

                // Add data-lenis-prevent attribute to dropdown when it opens
                $(this).on('select2:open', function() {
                    setTimeout(function() {
                        $('.select2-dropdown').attr('data-lenis-prevent', '');
                        $('.select2-results__options').attr('data-lenis-prevent', '');

                        // Mobile specific adjustments
                        if (isMobile) {
                            // Center the dropdown on mobile
                            var dropdownWidth = $('.select2-dropdown').outerWidth();
                            var windowWidth = $(window).width();
                            var leftPosition = (windowWidth - dropdownWidth) / 2;

                            // Ensure dropdown is fully visible
                            $('.select2-dropdown').css({
                                'max-width': '90vw',
                                'left': Math.max(5, leftPosition) + 'px'
                            });

                            // Increase height for better touch interaction
                            $('.select2-results__options').css('max-height', '60vh');
                        }

                        // Prevent dropdown from closing when scrolling
                        $('.select2-results__options').off('mousewheel.select2 DOMMouseScroll.select2 touchstart.select2')
                            .on('mousewheel.select2 DOMMouseScroll.select2 touchstart.select2', function(e) {
                                var $this = $(this), scrollTop = $this.scrollTop();

                                // Check if we're at the top or bottom of the scroll container
                                if (e.type === 'mousewheel' || e.type === 'DOMMouseScroll') {
                                    var delta = e.originalEvent.wheelDelta || -e.originalEvent.detail;

                                    // If we're at the top and scrolling up, or at the bottom and scrolling down, prevent propagation
                                    if ((scrollTop === 0 && delta > 0) ||
                                        (scrollTop >= $this[0].scrollHeight - $this.outerHeight() && delta < 0)) {
                                        e.preventDefault();
                                        e.stopPropagation();
                                    }
                                }
                            });
                    }, 0);
                });

                // Remove data-lenis-prevent attribute when dropdown closes
                $(this).on('select2:close', function() {
                    $('.select2-dropdown').removeAttr('data-lenis-prevent');
                    $('.select2-results__options').removeAttr('data-lenis-prevent');
                });
            }
        });

        // Handle filter changes
        $('.recordFilter').off('change.recordFilter').on('change.recordFilter', function() {
            filterRecords();
        });

        // Set filter values from URL on page load
        setFiltersFromUrl();
    }

    // Set filter values from URL parameters
    function setFiltersFromUrl() {
        var urlParams = new URLSearchParams(window.location.search);
        var filtersChanged = false;

        // Set genre filter
        if (urlParams.has('record_genre')) {
            $('#genre-filter').val(urlParams.get('record_genre')).trigger('change.select2');
            filtersChanged = true;
        } else {
            // Default to 'all' if not specified
            $('#genre-filter').val('all').trigger('change.select2');
            filtersChanged = true;
        }

        // Set artist filter
        if (urlParams.has('record_artist')) {
            $('#artist-filter').val(urlParams.get('record_artist')).trigger('change.select2');
            filtersChanged = true;
        } else {
            // Default to 'all' if not specified
            $('#artist-filter').val('all').trigger('change.select2');
            filtersChanged = true;
        }

        // Set sort filter
        if (urlParams.has('sort')) {
            $('#sort-filter').val(urlParams.get('sort')).trigger('change.select2');
            filtersChanged = true;
        } else {
            // Default sorting to newest first if not specified
            $('#sort-filter').val('date_desc').trigger('change.select2');
            filtersChanged = true;
        }

        // If filters were changed but no AJAX request was triggered,
        // we need to initialize the pagination links
        if (filtersChanged) {
            // Initialize pagination links after a short delay to ensure select2 is fully initialized
            setTimeout(function() {
                initPaginationLinks();
            }, 100);
        }
    }

    // Filter records based on selected options
    function filterRecords() {
        var genre = $('#genre-filter').val();
        var artist = $('#artist-filter').val();
        var sort = $('#sort-filter').val();

        // Reset to page 1 when filters change
        var paged = 1;

        // Build the URL with filter parameters
        if (!recordsArchiveData) {
            location.reload();
            return;
        }

        var url = recordsArchiveData.archiveUrl;
        var queryParams = {};

        // Only add genre if it's not 'all'
        if (genre && genre !== 'all') {
            queryParams.record_genre = genre;
        }

        // Only add artist if it's not 'all'
        if (artist && artist !== 'all') {
            queryParams.record_artist = artist;
        }

        if (sort) {
            queryParams.sort = sort;
        }

        // Always include page 1 when filtering
        queryParams.paged = paged;

        // Build the URL with query parameters
        url = addQueryParams(url, queryParams);

        // Update browser URL without reloading the page
        window.history.pushState({}, '', url);

        // Show loading state
        $('.recordsGrid').addClass('loading');

        // Fetch filtered records via AJAX
        $.ajax({
            url: recordsArchiveData.ajaxUrl,
            type: 'POST',
            data: {
                action: 'filter_records',
                genre: genre,
                artist: artist,
                sort: sort,
                paged: paged,
                nonce: recordsArchiveData.nonce
            },
            success: function(response) {
                // Replace the records grid with the new content
                $('.recordsGrid').html(response.data.html);
                $('.pagination').html(response.data.pagination);

                // Re-initialize pagination links
                initPaginationLinks();

                // Initialize lazy loading for new images
                lazyLoadImages();

                // Remove loading state
                $('.recordsGrid').removeClass('loading');
            },
            error: function() {
                // Remove loading state and show error message
                $('.recordsGrid').removeClass('loading');
                $('.recordsGrid').html('<div class="noRecords"><p>' + recordsArchiveData.errorMessage + '</p></div>');
            }
        });
    }

})(jQuery);
