var parallaxLoadOffset = 1000;
var parallaxIndicator = 20;
document.fonts.ready.then(function(){
    $(document).on("initPage", function(){
      if($(window).width() < $(window).height()){
          parallaxIndicator = 30;
      }
      changeparallaxElements();
    });

});

function changeparallaxElements() {
    $("[data-parallax]").each(function(i, el){
        // Cache jQuery element to avoid repeated DOM queries
        const $el = $(el);
        const parallaxSpeed = $el.data("parallax-speed");
        const scrollPosition = $el.data("scroll-position");
        const scrollDirection = $el.data("scroll-direction");

        // Optimize performance by using requestAnimationFrame
        let ticking = false;
        let lastScrollY = 0;

        // Create a more efficient ScrollTrigger
        ScrollTrigger.create({
            trigger: el,
            start: "0% 100%",
            end: "100% 0%",
            onUpdate(self){
                if(self.isActive){
                    // Only update if we've scrolled and not currently processing
                    if (!ticking && lastScrollY !== currentScrollY) {
                        lastScrollY = currentScrollY;

                        requestAnimationFrame(() => {
                            let position;

                            if(scrollPosition === "top"){
                                if(($(window).scrollTop() / parallaxIndicator) * parallaxSpeed < 0){
                                    position = Math.abs((currentScrollY / (parallaxIndicator * 2)) * parallaxSpeed);
                                } else {
                                    position = -Math.abs((currentScrollY / (parallaxIndicator * 2)) * parallaxSpeed);
                                }
                            } else {
                                position = (($el.offset().top - currentScrollY - (($(window).height() / 2) - ($el.height() / 2))) / parallaxIndicator) * parallaxSpeed;
                            }

                            // Use hardware acceleration for smoother animations
                            if(scrollDirection === "horizontal"){
                                gsap.set(el, {x: position, force3D: true, overwrite: "auto"});
                            } else {
                                gsap.set(el, {y: position, force3D: true, overwrite: "auto"});
                            }

                            ticking = false;
                        });

                        ticking = true;
                    }
                }
            }
        });
    });
}
