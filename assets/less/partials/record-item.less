// out: false
@import '../vw_values.less';
@import '../constants.less';
// Base styles for recordItem
.recordItem, .recordSlide {
  transition: transform 0.3s ease;

  &:hover {
    transform: translateY(-@vw5);

    .recordCover img {
      transform: scale(1.05);
    }
  }

  .recordLink {
    display: block;
    text-decoration: none;
    color: @almostWhite;
  }

  .recordCover {
    position: relative;
    width: 100%;
    padding-bottom: 100%; // Square aspect ratio
    margin-bottom: @vw15;
    overflow: hidden;
    background: @vinylColor;
    .transform(translate3d(0,0,0));
    img {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      object-fit: cover;
      .transitionMore(transform, .3s);
    }

    .defaultCover {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      display: block;
      background: @vinylColor;
      .transform(translate3d(0,0,0));

      svg {
        width: 80%;
        height: 80%;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
      }
    }
  }

  .recordInfo {
    .recordTitle, .smallTitle {
      text-overflow: ellipsis;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      display: -webkit-box;
      line-height: 1;
      overflow: hidden;
    }

    .recordArtist {
      font-size: @vw16;
      font-family: @bodyFont;
      color: @secondaryColor;
      margin-bottom: @vw5;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    .recordFormat, .recordPrice {
      display: inline-block;
      line-height: 1;
      margin-top: @vw5;
      width: 49%;
    }
    .recordFormat {
      opacity: .5;
    }
    .recordPrice {
      letter-spacing: 1px;
      text-align: right;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
}

// Styles for record in a slider
.recordSlide {
  white-space: normal;
  display: inline-block;
  vertical-align: top;
  cursor: pointer;
  width: 25%;
  padding: 0 @vw15;
  .transitionMore(opacity, .45s);

  * {
    cursor: pointer;
  }
}

// Hover effect for sliders
body:not(.touch) {
  .recordsSlider:hover {
    .recordSlide {
      opacity: .4;
      &:hover {
        opacity: 1;
      }
    }
  }
}

// Responsive styles for tablet (1160px)
@media all and (max-width: 1160px) {
  .recordItem {
    &:hover {
      transform: translateY(-@vw5-1160);
    }

    .recordCover {
      margin-bottom: @vw15-1160;
    }

    .recordInfo {
      .recordTitle, .smallTitle {
        margin: 0 0 @vw5-1160;
      }

      .recordArtist {
        margin-bottom: @vw5-1160;
      }

      .recordPrice, .primary {
        margin-top: @vw5-1160;
      }
    }
  }

  .recordSlide {
    width: 33.333%;
    padding: 0 @vw15-1160;
  }
}

// Responsive styles for mobile (580px)
@media all and (max-width: 580px) {
  .recordItem {
    .recordCover {
      margin-bottom: @vw15-580;
    }

    .recordInfo {
      .recordTitle, .smallTitle {
        margin: 0 0 @vw5-580;
      }

      .recordArtist {
        margin-bottom: @vw5-580;
      }

      .recordPrice, .primary {
        margin-top: @vw5-580;
      }
    }
  }

  .recordSlide {
    width: 80%;
    padding: 0 @vw15-580;
  }
}
