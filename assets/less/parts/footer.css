/* Colors */
/* Dark record color */
/* Deeper black */
/* Vinyl red */
/* Light background */
/* Dark vinyl color */
/* Record label yellow */
/* Fonts */
.footer {
  padding: 4.051vw 0 1.157vw 0;
  position: relative;
  overflow: hidden;
}
.footer .innerWrapper {
  margin-bottom: 2.894vw;
  display: block;
  height: 0.116vw;
  position: relative;
}
.footer .partnersList {
  margin-top: 1.736vw;
}
.footer .partnersList .partner {
  width: auto;
  height: 0.984vw;
  display: inline-block;
  cursor: pointer;
  -webkit-transition: opacity 0.3s 0s ease-out;
  -moz-transition: opacity 0.3s 0s ease-out;
  -o-transition: opacity 0.3s 0s ease-out;
  transition: opacity 0.3s 0s ease-out;
}
.footer .partnersList .partner:hover {
  opacity: 0.4;
}
.footer .partnersList .partner img {
  width: auto;
  height: 100%;
  display: block;
}
.footer .topBorder {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  fill: #F5F5F5;
}
.footer .topBorder line {
  stroke: #F5F5F5;
  stroke-width: 1;
  stroke-dasharray: 5;
  animation: dash-move 2s linear infinite;
  stroke-dashoffset: 0;
  transition: stroke-dashoffset 0.3s ease;
}
.footer .backgroundWrapper {
  width: 60vw;
  position: absolute;
  height: 60vw;
  -webkit-transform: translateY(-50%);
  -moz-transform: translateY(-50%);
  -o-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  transform: translateY(-50%);
  top: 50%;
  opacity: 1;
  left: 0;
}
.footer .backgroundWrapper .background {
  opacity: 1;
  position: absolute;
  animation: moveBackground 10s infinite ease-in-out alternate;
  top: 0;
  left: 0;
  width: 100%;
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  border-radius: 50%;
  height: 100%;
  background: #FFFFFF;
  -webkit-mask-image: radial-gradient(#000000, rgba(0, 0, 0, 0), rgba(0, 0, 0, 0));
  mask-image: radial-gradient(#000000, rgba(0, 0, 0, 0), rgba(0, 0, 0, 0));
}
.footer .bottomFooter {
  margin-top: 1.273vw;
}
.footer .bottomFooter .col {
  display: inline-block;
  vertical-align: middle;
  width: 50%;
}
.footer .bottomFooter .col:last-child {
  text-align: right;
}
.footer .socials .social {
  display: inline-block;
  cursor: pointer;
  color: #FFFFFF;
  height: 2.315vw;
  width: 2.315vw;
  text-decoration: none;
  line-height: 2.315vw;
  text-align: center;
  background: rgba(255, 255, 255, 0.4);
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  border-radius: 50%;
  -webkit-transition: opacity 0.3s 0s ease-out;
  -moz-transition: opacity 0.3s 0s ease-out;
  -o-transition: opacity 0.3s 0s ease-out;
  transition: opacity 0.3s 0s ease-out;
}
.footer .socials .social:not(:last-child) {
  margin-right: 0.579vw;
}
.footer .socials .social:hover {
  opacity: 0.4;
}
.footer .socials .social i {
  font-size: 0.926vw;
  cursor: pointer;
}
.footer .logo {
  height: 3.472vw;
  margin-bottom: 2.025vw;
  cursor: pointer;
  width: auto;
  display: inline-block;
  vertical-align: middle;
  -webkit-transition: opacity 0.3s 0s ease-out;
  -moz-transition: opacity 0.3s 0s ease-out;
  -o-transition: opacity 0.3s 0s ease-out;
  transition: opacity 0.3s 0s ease-out;
}
.footer .logo:hover {
  opacity: 0.4;
}
.footer .logo svg {
  cursor: pointer;
  object-fit: contain;
  width: auto;
  height: 100%;
}
.footer .logo svg path,
.footer .logo svg rect {
  cursor: pointer;
  fill: #FFFFFF;
}
.footer .cols .col {
  display: inline-block;
  vertical-align: top;
  width: 25%;
}
.footer .partners-list {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -5px;
}
.footer .partners-list .partner-item {
  padding: 5px;
  width: 50%;
}
.footer .partners-list .partner-item a {
  display: block;
  transition: transform 0.3s ease;
}
.footer .partners-list .partner-item a:hover {
  transform: translateY(-3px);
}
.footer .partners-list .partner-item img {
  max-width: 100%;
  height: auto;
  display: block;
}
.footer .smallTitle {
  margin-bottom: 1.157vw;
}
.footer .innerMenu {
  display: inline-block;
  vertical-align: middle;
  list-style: none;
}
.footer .innerMenu li {
  display: block;
}
.footer .innerMenu a {
  display: inline-block;
  vertical-align: middle;
  padding: 0.289vw 0;
  cursor: pointer;
  color: #F5F5F5;
  text-decoration: none;
  -webkit-transition: opacity 0.3s 0s ease-out;
  -moz-transition: opacity 0.3s 0s ease-out;
  -o-transition: opacity 0.3s 0s ease-out;
  transition: opacity 0.3s 0s ease-out;
}
.footer .innerMenu a:not(:last-of-type) {
  margin-right: 1.273vw;
}
.footer .innerMenu a:hover {
  opacity: 0.4;
}
@keyframes moveBackground {
  0% {
    transform: translate(0, 0);
  }
  25% {
    transform: translate(10vw, -5vw);
  }
  50% {
    transform: translate(-10vw, 5vw);
  }
  75% {
    transform: translate(5vw, -10vw);
  }
  100% {
    transform: translate(0, 0);
  }
}
@keyframes dash-move {
  0%,
  100% {
    stroke-dashoffset: 0;
  }
  50% {
    stroke-dashoffset: -5;
  }
}
@media all and (max-width: 1160px) {
  .footer {
    padding: 6.034vw 0 1.724vw 0;
  }
  .footer .innerWrapper {
    margin-bottom: 4.31vw;
    height: 0.172vw;
  }
  .footer .partnersList {
    margin-top: 2.586vw;
  }
  .footer .partnersList .partner {
    height: 1.466vw;
  }
  .footer .partnersList .partner:not(:last-child) {
    margin-right: 0.862vw;
  }
  .footer .backgroundWrapper {
    width: 60vw;
    height: 60vw;
  }
  .footer .bottomFooter {
    margin-top: 1.897vw;
  }
  .footer .socials .social {
    height: 3.448vw;
    width: 3.448vw;
    line-height: 3.448vw;
  }
  .footer .socials .social:not(:last-child) {
    margin-right: 0.862vw;
  }
  .footer .socials .social i {
    font-size: 1.379vw;
  }
  .footer .logo {
    height: 5.172vw;
    margin-bottom: 3.017vw;
    width: auto;
  }
  .footer .cols {
    margin-left: -1.724vw;
    width: calc(100% + 3.448vw);
  }
  .footer .cols .col {
    margin: 0 1.724vw;
    width: calc(25% - 3.448vw);
  }
  .footer .partners-list {
    margin: 0 -3px;
  }
  .footer .partners-list .partner-item {
    padding: 3px;
  }
  .footer .smallTitle {
    margin-bottom: 1.724vw;
  }
  .footer .innerMenu {
    margin-left: 0;
  }
  .footer .innerMenu a {
    padding: 0.862vw;
  }
  .footer .innerMenu a:not(:last-of-type) {
    margin-right: 1.897vw;
  }
}
@media all and (max-width: 580px) {
  .footer {
    padding: 12.069vw 0 3.448vw 0;
    text-align: center;
  }
  .footer .innerWrapper {
    margin-bottom: 8.62vw;
    height: 0.344vw;
  }
  .footer .cols .col {
    width: 100%;
  }
  .footer .cols .col:nth-child(2),
  .footer .cols .col:nth-child(4) {
    display: none;
  }
  .footer .partnersList {
    margin-top: 5.172vw;
  }
  .footer .partnersList .partner {
    height: 2.931vw;
  }
  .footer .partnersList .partner:not(:last-child) {
    margin-right: 1.724vw;
  }
  .footer .backgroundWrapper {
    width: 120vw;
    height: 120vw;
  }
  .footer .bottomFooter {
    margin-top: 8.62vw;
    font-size: 3.793vw;
  }
  .footer .bottomFooter .col {
    width: 100%;
    text-align: center;
    margin-bottom: 3.448vw;
  }
  .footer .bottomFooter .col:last-child {
    text-align: center;
  }
  .footer .socials .social {
    height: 10.344vw;
    width: 10.344vw;
    line-height: 10.344vw;
    padding: 0;
    border-radius: 50%;
  }
  .footer .socials .social:not(:last-child) {
    margin-right: 2.758vw;
  }
  .footer .socials .social i {
    font-size: 3.793vw;
  }
  .footer .logo {
    height: 10.344vw;
    display: block;
    width: auto;
    margin: auto;
    margin-bottom: 6.034vw;
  }
  .footer .cols {
    margin-left: -3.448vw;
    width: calc(100% + 6.897vw);
  }
  .footer .cols .col {
    margin: 0 3.448vw;
    width: calc(100% - 6.897vw);
  }
  .footer .cols .col:not(:last-child) {
    margin-bottom: 5.172vw;
  }
  .footer .partners-list .partner-item {
    width: 33.333%;
  }
  .footer .smallTitle {
    margin-bottom: 3.448vw;
  }
  .footer .innerMenu {
    display: none;
  }
}
