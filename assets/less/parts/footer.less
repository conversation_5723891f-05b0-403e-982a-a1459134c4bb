@import '../vw_values.less';
@import '../constants.less';

.footer {
    padding: @vw70 0 @vw20 0;
    position: relative;
    overflow: hidden;
    .innerWrapper {
      margin-bottom: @vw50;
      display: block;
      height: @vw2;
      position: relative;
    }
    .partnersList {
      margin-top: @vw30;
      .partner {
        width: auto;
        height: @vw17;
        display: inline-block;
        cursor: pointer;
        .transitionMore(opacity, .3s);
        &:hover {
          opacity: .4;
        }
        img {
          width: auto;
          height: 100%;
          display: block;
        }
      }
    }
    .topBorder {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      fill: @almostWhite;
      line {
        stroke: @almostWhite;
        stroke-width: 1;
        stroke-dasharray: 5;
        animation: dash-move 2s linear infinite;
        stroke-dashoffset: 0;
        transition: stroke-dashoffset 0.3s ease;
      }
    }
    .backgroundWrapper {
      width: 60vw;
      position: absolute;
      height: 60vw;
      .transform(translateY(-50%));
      top: 50%;
      opacity: 1;
      left: 0;
      .background {
        opacity: 1;
        position: absolute;
        animation: moveBackground 10s infinite ease-in-out alternate;
        top: 0;
        left: 0;
        width: 100%;
        .rounded(50%);
        height: 100%;
        background: @hardWhite;
        -webkit-mask-image: radial-gradient(rgba(0,0,0,1), rgba(0,0,0,0), rgba(0,0,0,0));
        mask-image: radial-gradient(rgba(0,0,0,1), rgba(0,0,0,0), rgba(0,0,0,0));
      }
    }
    .bottomFooter {
        margin-top: @vw22;
        .col {
          display: inline-block;
          vertical-align: middle;
          width: 50%;
          &:last-child {
            text-align: right;
          }
        }
    }
    .socials {
      .social {
        display: inline-block;
        cursor: pointer;
        color: @hardWhite;
        height: @vw40;
        width: @vw40;
        text-decoration: none;
        line-height: @vw40;
        text-align: center;
        background: rgba(255,255,255,.4);
        .rounded(50%);
        .transitionMore(opacity, .3s);
        &:not(:last-child) {
          margin-right: @vw10;
        }
        &:hover {
          opacity: .4;
        }
        i {
          font-size: @vw16;
          cursor: pointer;
        }
      }
    }
    .logo {
      height: @vw60;
      margin-bottom: @vw35;
      cursor: pointer;
      width: auto;
      display: inline-block;
      vertical-align: middle;
      .transitionMore(opacity, .3s);
      &:hover {
        opacity: .4;
      }
      svg {
        cursor: pointer;
        object-fit: contain;
        width: auto;
        height: 100%;
        path, rect {
          cursor: pointer;
          fill: @hardWhite;
        }
      }
    }
    .cols {
      .col {
        display: inline-block;
        vertical-align: top;
        width: 25%;
      }
    }

    .partners-list {
      display: flex;
      flex-wrap: wrap;
      margin: 0 -5px;

      .partner-item {
        padding: 5px;
        width: 50%;

        a {
          display: block;
          transition: transform 0.3s ease;

          &:hover {
            transform: translateY(-3px);
          }
        }

        img {
          max-width: 100%;
          height: auto;
          display: block;
        }
      }
    }
    .smallTitle {
      margin-bottom: @vw20;
    }
    .innerMenu {
      display: inline-block;
      vertical-align: middle;
      list-style: none;
      li {
        display: block;
      }
      a {
          display: inline-block;
          vertical-align: middle;
          padding: @vw5 0;
          cursor: pointer;
          color: @almostWhite;
          text-decoration: none;
          .transitionMore(opacity, .3s);
          &:not(:last-of-type) {
              margin-right: @vw22;
          }
          &:hover {
              opacity: .4;
          }
      }
  }
}

@keyframes moveBackground {
  0% {
    transform: translate(0, 0);
  }
  25% {
    transform: translate(10vw, -5vw);
  }
  50% {
    transform: translate(-10vw, 5vw);
  }
  75% {
    transform: translate(5vw, -10vw);
  }
  100% {
    transform: translate(0, 0);
  }
}

@keyframes dash-move {
  0%, 100% {
    stroke-dashoffset: 0;
  }
  50% {
    stroke-dashoffset: -5;
  }
}

// Responsive styles
@media all and (max-width: 1160px) {
  .footer {
    padding: @vw70-1160 0 @vw20-1160 0;

    .innerWrapper {
      margin-bottom: @vw50-1160;
      height: @vw2-1160;
    }

    .partnersList {
      margin-top: @vw30-1160;

      .partner {
        height: @vw17-1160;

        &:not(:last-child) {
          margin-right: @vw10-1160;
        }
      }
    }

    .backgroundWrapper {
      width: 60vw;
      height: 60vw;
    }

    .bottomFooter {
      margin-top: @vw22-1160;
    }

    .socials {
      .social {
        height: @vw40-1160;
        width: @vw40-1160;
        line-height: @vw40-1160;

        &:not(:last-child) {
          margin-right: @vw10-1160;
        }

        i {
          font-size: @vw16-1160;
        }
      }
    }

    .logo {
      height: @vw60-1160;
      margin-bottom: @vw35-1160;
      width: auto;
    }

    .cols {
      margin-left: -@vw20-1160;
      width: calc(100% + @vw40-1160);

      .col {
        margin: 0 @vw20-1160;
        width: calc(25% - @vw40-1160);
      }
    }

    .partners-list {
      margin: 0 -3px;

      .partner-item {
        padding: 3px;
      }
    }

    .smallTitle {
      margin-bottom: @vw20-1160;
    }

    .innerMenu {
      margin-left: 0;

      a {
        padding: @vw10-1160;

        &:not(:last-of-type) {
          margin-right: @vw22-1160;
        }
      }
    }
  }
}

@media all and (max-width: 580px) {
  .footer {
    padding: @vw70-580 0 @vw20-580 0;
    text-align: center;
    .innerWrapper {
      margin-bottom: @vw50-580;
      height: @vw2-580;
    }

    .cols {
      .col {
        width: 100%;
        &:nth-child(2), &:nth-child(4) {
          display: none;
        }
      }
    }

    .partnersList {
      margin-top: @vw30-580;

      .partner {
        height: @vw17-580;

        &:not(:last-child) {
          margin-right: @vw10-580;
        }
      }
    }

    .backgroundWrapper {
      width: 120vw;
      height: 120vw;
    }

    .bottomFooter {
      margin-top: @vw50-580;
      font-size: @vw22-580;

      .col {
        width: 100%;
        text-align: center;
        margin-bottom: @vw20-580;
        &:last-child {
          text-align: center;
        }
      }
    }

    .socials {
      .social {
          height: @vw60-580;
          width: @vw60-580;
          line-height: @vw60-580;
          padding: 0;
          border-radius: 50%;

          &:not(:last-child) {
            margin-right: @vw16-580;
          }

          i {
            font-size: @vw22-580;
          }
        }
    }

    .logo {
      height: @vw60-580;
      display: block;
      width: auto;
      margin: auto;
      margin-bottom: @vw35-580;
    }

    .cols {
      margin-left: -@vw20-580;
      width: calc(100% + @vw40-580);
      .col {
        margin: 0 @vw20-580;
        width: calc(100% - @vw40-580);
        &:not(:last-child) {
          margin-bottom: @vw30-580;
        }
      }
    }

    .partners-list {
      .partner-item {
        width: 33.333%;
      }
    }

    .smallTitle {
      margin-bottom: @vw20-580;
    }

    .innerMenu {
      display: none;
    }
  }
}
