@import '../vw_values.less';
@import '../constants.less';

.signatureDD {
    display: inline-block;
    vertical-align: middle;
    cursor: pointer;
    * {
        cursor: pointer;
    }
    &:hover { 
        opacity: .4;
        transition: opacity .3s;
    }
    .linkDD {
        color: #ffffff;
        cursor: pointer;
        font-size: @vw18;
        display: inline-block;
        text-decoration: none;
        margin-right: @vw6;
    }
    .innerTextDD, .svgWrap {
        cursor: pointer;
        display: inline-block;
        vertical-align: middle;
    }
    .svgWrap { 
        cursor: pointer;
        display: inline-block;
        vertical-align: middle;
        width: @vw90;
        height: auto;
        svg {
            cursor: pointer;
            display: inline-block;
            vertical-align: middle;
            width: 100%;
            height: auto;
            object-fit: contain;
            path, rect {
                fill: #ffffff;
            }
        }
    }
}

@media all and (max-width: 1160px) {
    .signatureDD {
        .linkDD {
            margin-right: @vw6-1160;
            font-size: @vw16-1160;
        }
        .svgWrap { 
            width: @vw80-1160;
        }
    }
}

@media all and (max-width: 580px) {
    .signatureDD {
        .linkDD {
            margin-right: @vw6-580;
            font-size: @vw24-580;
        }
        .svgWrap { 
            width: @vw120-580 + @vw10-580;
        }
    }
}