// out: ../../style.css, compress: true, strictMath: true

/*
Theme Name: Rewindrecords
Author: <PERSON>
Version: 1.0.0
Description: A custom theme for Rewindrecords record store with Discogs integration
*/

@import 'vw_values.less';
@import 'constants.less';
@import 'default.less';
@import 'typo.less';
@import 'parts/header.less';
@import 'parts/footer.less';
@import 'parts/overlay.less';
@import 'parts/ddsignature.less';
@import 'partials/record-item.less';
@import 'records.less';

// blocks
@import '../../blocks/less/home-header-block.less';
@import '../../blocks/less/projects-home-block.less';
@import '../../blocks/less/image-quote-block.less';
@import '../../blocks/less/projects-block.less';
@import '../../blocks/less/small-header-block.less';
@import '../../blocks/less/small-header-links-block.less';
@import '../../blocks/less/text-block.less';
@import '../../blocks/less/text-slider-block.less';
@import '../../blocks/less/project-info-block.less';
@import '../../blocks/less/project-header-block.less';
@import '../../blocks/less/records-block.less';
@import '../../blocks/less/random-records-slider-block.less';
@import '../../blocks/less/latest-records-block.less';
@import '../../blocks/less/record-of-the-week-block.less';
@import '../../blocks/less/contact-block.less';
@import '../../blocks/less/rewind-home-header-block.less';
@import '../../blocks/less/media-text-block.less';
@import '../../blocks/less/spotify-text-block.less';
@import '../../blocks/less/images-text-block.less';
@import '../../blocks/less/about-block.less';
@import '../../blocks/less/small-text-marquee-block.less';
@import '../../blocks/less/text-marquee-block.less';
@import '../../blocks/less/global-info-block.less';

@font-face {
  font-family: 'icomoon';
  src:  url('assets/fonts/icomoon.eot?twq3hi');
  src:  url('assets/fonts/icomoon.eot?twq3hi#iefix') format('embedded-opentype'),
    url('assets/fonts/icomoon.woff?twq3hi') format('woff'),
    url('assets/fonts/icomoon.ttf?twq3hi') format('truetype'),
    url('assets/fonts/icomoon.svg?twq3hi#icomoon') format('svg');
  font-weight: normal;
  font-style: normal;
  font-display: block;
}

[class^="icon-"], [class*=" icon-"] {
  /* use !important to prevent issues with browser extensions that change fonts */
  font-family: 'icomoon' !important;
  /* speak: never; */ /* Verouderde eigenschap verwijderd */
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;
  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-arrow-right-up:before {
  content: "\e900";
}
.icon-arrow-right:before {
  content: "\e901";
}
.icon-arrow-left:before {
  content: "\e902";
}
.icon-arrow-right-down:before {
  content: "\e903";
}
.icon-arrow-down:before {
  content: "\e904";
}
.icon-arrow-up:before {
  content: "\e905";
}
.icon-insta:before {
  content: "\e906";
}
.icon-tiktok:before {
  content: "\e907";
}
.icon-phone:before {
  content: "\e908";
}
.icon-mail:before {
  content: "\e909";
}
.icon-marker:before {
  content: "\e90a";
}
.icon-facebook:before {
  content: "\e90b";
}

::-webkit-scrollbar {
  width: @vw10;
}

::-webkit-scrollbar-track {
  background: @almostWhite;
}

::-webkit-scrollbar-thumb {
  border-radius: @vw50;
  background: rgba(0,0,0,.1);
}

.block__headline {
    padding: 20px 15px 30px;
    background: #fafafa;
    text-align: center;
}
.block__headline-title {
    font-family: 'Arial', sans-serif;
    font-size: 30px;
    font-weight: bold;
    position: relative;
}
.block__headline-title:after {
    content: '';
    display: block;
    width: 40px;
    height: 2px;
    background: #333;
    margin: 0 auto;
}

html.has-scroll-smooth {
	backface-visibility: hidden;
	transform: translateZ(0);
  [data-load-container] {
  	position: fixed;
  	top: 0;
  	right: 0;
  	bottom: 0;
  	left: 0;
  	width: 100vw;
  }
}

// Swup

.transition-fade {
  transition: .75s;
  opacity: 1;
}

html.is-animating .transition-fade {
  opacity: 0;
}

.grecaptcha-badge {
  visibility: hidden;
}

@media all and (max-width: 1160px) {

}

@media all and (max-width: 580px) {

}

@keyframes dash-move {
  from {
    stroke-dashoffset: 0; /* Startpositie */
  }
  to {
    stroke-dashoffset: -20; /* Naar links verschuiven */
  }
}