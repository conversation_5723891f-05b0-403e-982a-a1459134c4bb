// out: false
@import 'vw_values.less';
@import 'constants.less';
@import 'partials/record-item.less';

// Records Archive
.recordsArchive {
  padding: @vw50 0; 

  .bigTitle {
    margin-bottom: @vw30;
  }

  .recordFilters {
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-start;
    align-items: flex-end;
    margin: 0 -@vw10;
    margin-bottom: @vw40;
    padding: @vw25;
    background: @almostBlack;
    border-radius: @vw10;
    position: relative;
    .filterGroup {
      display: inline-block;
      margin: 0 @vw15 @vw15 @vw15;
      min-width: @vw200;

      label {
        display: block;
        margin-bottom: @vw8;
        color: @hardWhite;
        font-family: @headingFont;
        font-size: @vw18;
        letter-spacing: 1px;
        text-transform: uppercase;
      }

      select {
        display: block;
        width: 100%;
        padding: @vw10 @vw15;
        border: 2px solid @hardWhite;
        border-radius: @vw5;
        background: @hardBlack;
        color: @hardWhite;
        min-width: @vw200;
        font-family: @headingFont;
        font-size: @vw16;
        letter-spacing: 1px;
        text-transform: uppercase;
        transition: all 0.3s ease;
        cursor: pointer;

        &:hover {
          border-color: @secondaryColor;
        }

        &:focus {
          border-color: @secondaryColor;
          outline: none;
          box-shadow: 0 0 0 2px rgba(232, 58, 20, 0.3);
        }

        option {
          font-family: @headingFont;
          font-size: @vw16;
          letter-spacing: 1px;
          text-transform: uppercase;
          padding: @vw8;
        }
      }
    }
  }

  .recordsGrid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(@vw250, 1fr));
    gap: @vw30;

    // Record item styling is now imported from partials/record-item.less

    .noRecords {
      grid-column: 1 / -1;
      text-align: center;
      padding: @vw50 0;

      p {
        margin-bottom: @vw20;
        color: @almostWhite;
        font-size: @vw18;
      }

      .button {
        display: inline-block;
        padding: @vw10 @vw20;
        background: @secondaryColor;
        color: @hardWhite;
        text-decoration: none;
        border-radius: @vw5;
        font-weight: bold;

        &:hover {
          background: darken(@secondaryColor, 10%);
        }
      }
    }
  }

  .pagination {
    margin-top: @vw40;
    text-align: center;
    display: block;
    width: 100%;
    .page-numbers {
      display: inline-block;
      margin: 0 @vw5;
      border: 1px solid @grey;
      .border-radius(50%,50%,50%,50%);
      color: @almostWhite;
      text-decoration: none;
      font-family: "Bebas Neue", cursive;
      width: @vw50;
      height: @vw50;
      cursor: pointer;
      line-height: @vw48;
      transition: border-color 0.3s ease, color 0.3s ease;
      -webkit-transition: border-color 0.3s ease, color 0.3s ease;
      &.current {
        background: @primaryColor;
        border-color: @primaryColor;
        color: @hardWhite;
      }
      &.prev, &.next {
        width: auto;
        .border-radius(@vw50,@vw50,@vw50,@vw50);
        padding: @vw5 @vw15;
        line-height: auto;
        height: auto;
      }
      &:hover:not(.current) {
        border-color: @primaryColor;
        color: @primaryColor;
      }
    }
  }

  .termDescription {
    margin-bottom: @vw30;
    color: @almostWhite;
    font-size: @vw16;
    line-height: 1.5;
  }
}

// Single Record
.singleRecord {
  padding: @vw50 0;

  .recordHeader {
    margin-bottom: @vw40;

    .bigTitle {
      margin-bottom: @vw10;
    }

    .recordArtist {
      font-size: @vw40;
      font-family: @headingFont;
      color: @primaryColor;
      a {
        color: @primaryColor;
        text-decoration: none;

        &:hover {
          text-decoration: underline;
        }
      }
    }
  }

  .recordContent {
    display: flex;
    flex-wrap: wrap;
    .recordCoverCol {
      display: inline-block;
      vertical-align: top;
      width: 40%;
      padding: 0 @vw20;
      min-width: @vw300;

      .recordCover {
        width: 100%;
        max-width: @vw100 * 4;
        margin-bottom: @vw30;
        border-radius: @vw10;
        overflow: hidden;
        box-shadow: 0 @vw10 @vw30 rgba(0, 0, 0, 0.4);

        img {
          width: 100%;
          height: auto;
          display: block;
        }

        .defaultCover {
          width: 100%;
          padding-bottom: 100%;
          position: relative;
          background: @vinylColor;

          svg {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
          }
        }
      }

      .recordMeta {
        background: @almostBlack;
        padding: @vw20;
        border-radius: @vw10;

        .metaItem {
          margin-bottom: @vw15;
          display: flex;
          flex-wrap: wrap;

          &:last-child {
            margin-bottom: 0;
          }

          .metaLabel {
            display: inline-block;
            width: @vw100;
            font-family: @headingFont;
            font-weight: 400;
            letter-spacing: 1px;
            color: @almostWhite;
          }

          .metaValue {
            color: @grey;
            font-family: @headingFont;
            display: inline-block;
            flex: 1;

            a {
              color: @secondaryColor;
              text-decoration: none;

              &:hover {
                text-decoration: underline;
              }
            }

            &.suggestedPrice {
              color: @secondaryColor;
              font-family: @headingFont;
              font-weight: 400;
              letter-spacing: 1px;
              font-size: @vw18;
            }
          }
        }
      }
    }

    .recordInfoCol {
      display: inline-block;
      vertical-align: top;
      width: 60%;
      padding: 0 @vw20;
      min-width: @vw300;

      .recordTracklist {
        margin-bottom: @vw40;

        .tracklistTable {
          width: 100%;
          border-collapse: collapse;

          th, td {
            padding: @vw10;
            text-align: left;
            border-bottom: 1px solid @vinylColor;
          }

          th {
            color: @almostWhite;
            font-family: @headingFont;
            font-weight: 400;
            letter-spacing: 1px;
          }

          td {
            color: @grey;
            font-family: @bodyFont;
          }

          .trackPosition {
            width: @vw80;
          }

          .trackDuration {
            width: @vw100;
            text-align: right;
          }

          tr:hover td {
            background: @almostBlack;
            color: @almostWhite;
          }
        }
      }
    }
  }
}

// Responsive styles
@media all and (max-width: 1160px) {
  .recordsArchive {
    padding: @vw50-1160 0;

    .bigTitle {
      margin-bottom: @vw30-1160;
    }

    .recordFilters {
      margin: 0 0 @vw40-1160 0;
      padding: @vw25-1160;
      border-radius: @vw10-1160;
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
      .filterGroup {
        margin: 0 @vw10-1160 @vw15-1160 @vw10-1160;
        min-width: @vw180-1160;
        width: calc(33.333% - @vw20-1160);

        label {
          margin-bottom: @vw8-1160;
          font-size: @vw18-1160;
        }

        select {
          padding: @vw10-1160 @vw15-1160;
          border-radius: @vw5-1160;
          min-width: auto;
          width: 100%;
          font-size: @vw16-1160;
          border-width: 2px;

          &:focus {
            box-shadow: 0 0 0 2px rgba(232, 58, 20, 0.3);
          }

          option {
            font-size: @vw16-1160;
            padding: @vw8-1160;
          }
        }
      }
    }

    .recordsGrid {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: @vw20-1160;

      // Record item styling is now imported from partials/record-item.less

      .noRecords {
        padding: @vw50-1160 0;
        grid-column: 1 / -1;

        p {
          margin-bottom: @vw20-1160;
          font-size: @vw18-1160;
        }

        .button {
          padding: @vw10-1160 @vw20-1160;
          border-radius: @vw5-1160;
        }
      }
    }

    .pagination {
      margin-top: @vw40-1160;

      .page-numbers {
        width: @vw50-1160;
        height: @vw50-1160;
        line-height: @vw48-1160;
        margin: 0 @vw5-1160;

        &.prev, &.next {
          width: auto;
          padding: @vw5-1160 @vw15-1160;
          line-height: auto;
          height: auto;
          border-radius: @vw50-1160;
        }
      }
    }
  }

  .singleRecord {
    padding: @vw50-1160 0;

    .recordHeader {
      margin-bottom: @vw40-1160;

      .bigTitle {
        margin-bottom: @vw10-1160;
      }

      .recordArtist {
        font-size: @vw24-1160;
      }
    }

    .recordContent {
      display: flex;
      flex-wrap: wrap;
      gap: @vw40-1160;

      .recordCoverCol {
        display: inline-block;
        vertical-align: top;
        width: 40%;
        padding: 0 @vw20-1160;
        min-width: @vw300-1160;

        .recordCover {
          width: 100%;
          max-width: @vw400-1160;
          margin-bottom: @vw30-1160;
          border-radius: @vw10-1160;
          box-shadow: 0 @vw10-1160 @vw30-1160 rgba(0, 0, 0, 0.4);
          overflow: hidden;

          img {
            width: 100%;
            height: auto;
            display: block;
          }
        }

        .recordMeta {
          padding: @vw20-1160;
          border-radius: @vw10-1160;
          background: @almostBlack;

          .metaItem {
            margin-bottom: @vw15-1160;
            display: flex;
            flex-wrap: wrap;

            &:last-child {
              margin-bottom: 0;
            }

            .metaLabel {
              width: @vw100-1160;
              font-weight: 400;
              letter-spacing: 1px;
              color: @almostWhite;
              display: inline-block;
            }

            .metaValue {
              color: @grey;
              display: inline-block;
              flex: 1;

              a {
                color: @secondaryColor;
                text-decoration: none;

                &:hover {
                  text-decoration: underline;
                }
              }

              &.suggestedPrice {
                color: @secondaryColor;
                font-family: @headingFont;
                font-weight: 400;
                letter-spacing: 1px;
                font-size: @vw18-1160;
              }
            }
          }
        }
      }

      .recordInfoCol {
        display: inline-block;
        vertical-align: top;
        width: calc(60% - @vw40-1160);
        padding: 0 @vw20-1160;
        min-width: @vw300-1160;

        .recordTracklist {
          margin-bottom: @vw40-1160;

          .tracklistTable {
            width: 100%;
            border-collapse: collapse;

            th, td {
              padding: @vw10-1160;
              text-align: left;
              border-bottom: 1px solid @vinylColor;
            }

            th {
              color: @almostWhite;
              font-family: @headingFont;
              font-weight: 400;
              letter-spacing: 1px;
            }

            td {
              color: @grey;
              font-family: @bodyFont;
            }

            .trackPosition {
              width: @vw80-1160;
            }

            .trackDuration {
              width: @vw100-1160;
              text-align: right;
            }

            tr:hover td {
              background: @almostBlack;
              color: @almostWhite;
            }
          }
        }
      }
    }
  }
}

@media all and (max-width: 580px) {
  .recordsArchive {
    padding: @vw50-580 0;

    .bigTitle {
      margin-bottom: @vw30-580;
      text-align: center;
    }

    .recordFilters {
      margin: 0 0 @vw30-580 0;
      padding: @vw20-580;
      border-radius: @vw10-580;
      display: block;

      .filterGroup {
        display: block;
        width: 100%;
        margin: 0 0 @vw20-580 0;

        &:last-child {
          margin-bottom: 0;
        }

        label {
          display: block;
          margin: 0 0 @vw8-580 0;
          font-size: @vw18-580;
          text-align: center;
        }

        select {
          width: 100%;
          padding: @vw12-580 @vw15-580;
          border-radius: @vw5-580;
          font-size: @vw16-580;
          border-width: 2px;
          text-align-last: center;

          &:focus {
            box-shadow: 0 0 0 2px rgba(232, 58, 20, 0.3);
          }

          option {
            font-size: @vw16-580;
            padding: @vw8-580;
            text-align: left;
          }
        }
      }
    }

    .recordsGrid {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: @vw15-580;

      // Record item styling is now imported from partials/record-item.less

      .noRecords {
        padding: @vw30-580 0;
        grid-column: 1 / -1;

        p {
          margin-bottom: @vw15-580;
          font-size: @vw16-580;
        }

        .button {
          padding: @vw8-580 @vw15-580;
          border-radius: @vw5-580;
          font-size: @vw14-580;
        }
      }
    }

    .pagination {
      margin-top: @vw30-580;

      .page-numbers {
        width: @vw40-580;
        height: @vw40-580;
        line-height: @vw38-580;
        margin: 0 @vw3-580;
        font-size: @vw14-580;

        &.prev, &.next {
          width: auto;
          padding: @vw5-580 @vw10-580;
          line-height: auto;
          height: auto;
          border-radius: @vw40-580;
          font-size: @vw12-580;

          i {
            font-size: @vw12-580;
          }
        }
      }
    }
  }

  .singleRecord {
    padding: @vw30-580 0;

    .recordHeader {
      margin-bottom: @vw30-580;
      text-align: center;

      .bigTitle {
        margin-bottom: @vw10-580;
      }

      .recordArtist {
        font-size: @vw40-580;
        text-align: center;
      }
    }

    .recordContent {
      margin: 0;
      display: block;

      .recordCoverCol,
      .recordInfoCol {
        display: block;
        width: 100%;
        padding: 0;
        margin-bottom: @vw30-580;
      }

      .recordCoverCol {
        .recordCover {
          max-width: 100%;
          margin: 0 auto @vw20-580;
          border-radius: @vw10-580;
          box-shadow: 0 @vw10-580 @vw20-580 rgba(0, 0, 0, 0.4);
        }

        .recordMeta {
          padding: @vw20-580;
          border-radius: @vw10-580;
          background: @almostBlack;

          .metaItem {
            margin-bottom: @vw15-580;
            display: block;

            &:last-child {
              margin-bottom: 0;
            }

            .metaLabel {
              width: 48%;
              display: inline-block;
              margin-bottom: @vw5-580;
              font-size: @vw32-580;
            }

            .metaValue {
              display: inline-block;
              width: 48%;
              text-align: right;
              font-size: @vw32-580;

              &.suggestedPrice {
                font-size: @vw40-580;
              }
            }
          }
        }
      }

      .recordInfoCol {
        .recordTracklist {
          overflow-x: auto;
          margin-bottom: @vw30-580;
          -webkit-overflow-scrolling: touch;

          .tracklistTable {
            min-width: 100%;

            th, td {
              padding: @vw8-580;
              font-size: @vw22-580;
            }

            th {
              white-space: nowrap;
            }

            .trackPosition {
              width: @vw40-580;
            }

            .trackDuration {
              width: @vw60-580;
            }
          }
        }
      }
    }
  }
}
