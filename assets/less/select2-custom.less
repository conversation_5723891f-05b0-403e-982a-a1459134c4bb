// out: ../css/select2-custom.css, compress: true, strictMath: true
@import 'vw_values.less';
@import 'constants.less';

// Custom Select2 Styles for Rewindrecords Theme
.recordsArchive {
  .filterGroup {
    label {
      font-family: @headingFont;
      font-size: @vw18;
      letter-spacing: 1px;
      text-transform: uppercase;
      margin-right: @vw12;
    }
  }
}

.select2-container {
  width: 100% !important;

  .select2-selection--single {
    height: auto;
    background-color: @hardBlack;
    border: 2px solid @hardWhite;
    border-radius: @vw5;
    box-shadow: 0 @vw4 @vw8 rgba(0, 0, 0, 0.3);
    transition: all 0.3s ease;

    .select2-selection__rendered {
      color: @hardWhite;
      line-height: 1.5;
      padding: @vw10 @vw15;
      font-family: @headingFont;
      font-size: @vw18;
      letter-spacing: 1px;
      text-transform: uppercase;
    }

    .select2-selection__arrow {
      height: 100%;
      width: @vw30;

      b {
        display: none;
      }

      &:after {
        content: "\e901"; // icon-arrow-right
        font-family: 'icomoon' !important;
        position: absolute;
        top: 50%;
        right: @vw10;
        transform: translateY(-50%) rotate(90deg);
        color: @hardWhite;
        font-size: @vw16;
        transition: transform 0.3s ease;
      }
    }

    &:hover {
      border-color: @secondaryColor;
    }
  }

  &.select2-container--open {
    .select2-selection--single {
      border-color: @secondaryColor;

      .select2-selection__arrow {
        &:after {
          transform: translateY(-50%) rotate(-90deg);
          color: @secondaryColor;
        }
      }
    }
  }
}

// Dropdown styling
.select2-dropdown {
  background-color: @hardBlack;
  border: 2px solid @secondaryColor;
  border-radius: 0 0 @vw5 @vw5;
  margin-top: -2px;
  box-shadow: 0 @vw8 @vw16 rgba(0, 0, 0, 0.5);
  overflow: hidden;

  .select2-search--dropdown {
    padding: @vw10;
    background-color: @almostBlack;

    .select2-search__field {
      background-color: @hardBlack;
      color: @hardWhite;
      border: 2px solid @grey;
      border-radius: @vw3;
      padding: @vw8 @vw12;
      font-family: @bodyFont;

      &:focus {
        border-color: @secondaryColor;
        outline: none;
      }
    }
  }

  .select2-results {
    max-height: @vw300;

    &__options {
      max-height: @vw300 !important;
      overflow-y: auto !important;
      scrollbar-width: thin;
      scrollbar-color: @secondaryColor @almostBlack;

      &::-webkit-scrollbar {
        width: @vw8;
      }

      &::-webkit-scrollbar-track {
        background: @almostBlack;
        border-radius: @vw4;
      }

      &::-webkit-scrollbar-thumb {
        background-color: @secondaryColor;
        border-radius: @vw4;
        border: 2px solid @almostBlack;
      }
    }
  }

  .select2-results__option {
    padding: @vw10 @vw15;
    color: @hardWhite;
    font-family: @headingFont;
    font-size: @vw16;
    letter-spacing: 1px;
    text-transform: uppercase;
    transition: all 0.2s ease;
    cursor: pointer;

    &--highlighted.select2-results__option--selectable {
      background-color: @secondaryColor;
      color: @hardWhite;
    }

    &--selected {
      background-color: @almostBlack;
      color: @secondaryColor;
    }
  }
}

// Custom class for record filter dropdowns
.records-filter-dropdown {
  z-index: 1060;
}

// Loading state for records grid
.recordsGrid {
  transition: all 0.3s ease;
  position: relative;

  &.loading {
    opacity: 0.6;
    filter: blur(1px);

    &:before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(0, 0, 0, 0.2);
      z-index: 10;
    }

    &:after {
      content: '';
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: @vw60;
      height: @vw60;
      border: @vw4 solid rgba(255, 255, 255, 0.1);
      border-top: @vw4 solid @secondaryColor;
      border-bottom: @vw4 solid @primaryColor;
      border-radius: 50%;
      animation: spin 1s cubic-bezier(0.6, 0.2, 0.4, 0.8) infinite;
      box-shadow: 0 0 @vw10 rgba(232, 58, 20, 0.5);
      z-index: 11;
    }
  }
}

@keyframes spin {
  0% { transform: translate(-50%, -50%) rotate(0deg); }
  100% { transform: translate(-50%, -50%) rotate(360deg); }
}

// Responsive styles
@media all and (max-width: 1160px) {
  .recordsArchive {
    .filterGroup {
      label {
        font-size: @vw18-1160;
        margin-right: @vw12-1160;
      }
    }
  }

  .select2-container {
    .select2-selection--single {
      border-radius: @vw5-1160;
      border-width: 2px;
      box-shadow: 0 @vw4-1160 @vw8-1160 rgba(0, 0, 0, 0.3);

      .select2-selection__rendered {
        padding: @vw10-1160 @vw15-1160;
        font-size: @vw18-1160;
      }

      .select2-selection__arrow {
        width: @vw30-1160;

        &:after {
          right: @vw10-1160;
          font-size: @vw16-1160;
        }
      }
    }
  }

  .select2-dropdown {
    border-radius: 0 0 @vw5-1160 @vw5-1160;
    border-width: 2px;
    margin-top: -2px;
    box-shadow: 0 @vw8-1160 @vw16-1160 rgba(0, 0, 0, 0.5);

    .select2-search--dropdown {
      padding: @vw10-1160;

      .select2-search__field {
        border-radius: @vw3-1160;
        padding: @vw8-1160 @vw12-1160;
        border-width: 2px;
      }
    }

    .select2-results {
      max-height: @vw300-1160;

      &__options {
        max-height: @vw300-1160 !important;

        &::-webkit-scrollbar {
          width: @vw8-1160;
        }

        &::-webkit-scrollbar-track {
          border-radius: @vw4-1160;
        }

        &::-webkit-scrollbar-thumb {
          border-radius: @vw4-1160;
          border-width: 2px;
        }
      }
    }

    .select2-results__option {
      padding: @vw10-1160 @vw15-1160;
      font-size: @vw16-1160;
    }
  }

  .recordsGrid {
    &.loading {
      &:after {
        width: @vw60-1160;
        height: @vw60-1160;
        border-width: @vw4-1160;
        border-top-width: @vw4-1160;
        border-bottom-width: @vw4-1160;
        box-shadow: 0 0 @vw10-1160 rgba(232, 58, 20, 0.5);
      }
    }
  }
}

@media all and (max-width: 580px) {
  .recordsArchive {
    .filterGroup {
      label {
        font-size: @vw18-580;
        margin-right: 0;
        margin-bottom: @vw5-580;
        display: block;
      }
    }
  }

  .select2-container {
    .select2-selection--single {
      border-radius: @vw5-580;
      border-width: 2px;
      box-shadow: 0 @vw4-580 @vw8-580 rgba(0, 0, 0, 0.3);

      .select2-selection__rendered {
        padding: @vw10-580 @vw15-580;
        font-size: @vw18-580;
      }

      .select2-selection__arrow {
        width: @vw30-580;

        &:after {
          right: @vw10-580;
          font-size: @vw16-580;
        }
      }
    }
  }

  .select2-dropdown {
    border-radius: 0 0 @vw5-580 @vw5-580;
    border-width: 2px;
    margin-top: -2px;
    box-shadow: 0 @vw8-580 @vw16-580 rgba(0, 0, 0, 0.5);

    .select2-search--dropdown {
      padding: @vw10-580;

      .select2-search__field {
        border-radius: @vw3-580;
        padding: @vw8-580 @vw12-580;
        border-width: 2px;
      }
    }

    .select2-results {
      max-height: @vw250-580;

      &__options {
        max-height: @vw250-580 !important;

        &::-webkit-scrollbar {
          width: @vw6-580;
        }

        &::-webkit-scrollbar-track {
          border-radius: @vw3-580;
        }

        &::-webkit-scrollbar-thumb {
          border-radius: @vw3-580;
          border-width: 1px;
        }
      }
    }

    .select2-results__option {
      padding: @vw10-580 @vw15-580;
      font-size: @vw16-580;
    }
  }

  .recordsGrid {
    &.loading {
      &:after {
        width: @vw50-580;
        height: @vw50-580;
        border-width: @vw3-580;
        border-top-width: @vw3-580;
        border-bottom-width: @vw3-580;
        box-shadow: 0 0 @vw8-580 rgba(232, 58, 20, 0.5);
      }
    }
  }
}
