@import 'vw_values.less';

/* Colors */
@primaryColor: #CFBA9C;       /* Dark record color */
@primaryColorDark: #000000;  /* Deeper black */
@secondaryColor: #CFBA9C;    /* Vinyl red */
@secondaryColorLight: #F9F9F9; /* Light background */
@almostBlack: #1A1A1A;
@hardBlack: #000000;
@hardWhite: #FFFFFF;
@formBackground: #F9F9F9;
@grey: #B3B3B3;
@darkerGrey: #444444;
@lightGrey: #CCCCCC;
@borderGrey: #EEEEEE;
@almostWhite: #F5F5F5;
@backgroundGrey: #F9F9F9;
@vinylColor: #333333;        /* Dark vinyl color */
@recordLabelColor: #F5D042;  /* Record label yellow */

/* Fonts */
@headingFont: 'Bebas Neue', cursive;
@bodyFont: 'Lora', serif;

// font sizes
@fsHuge: @vw100;
@fsBig: @vw52;
@fsNormal: @vw32;
@fsDefault: @vw21;

// line heights
@lhHuge: @vw100;
@lhBig: @vw63;
@lhNormal: @vw39;
@lhDefault: @vw32;

.animation(@arguments) {
    -webkit-animation: @arguments;
    -moz-animation: @arguments;
    animation: @arguments;
}
.animationDelay(@delay) {
    -webkit-animation-delay: @delay;
    -moz-animation-delay: @delay;
    animation-delay: @delay;
}
.radialGradientMask(@innerColor, @outerColor, @innerSize: 40%) {
    mask-image: radial-gradient(circle, @innerColor @innerSize, @outerColor 100%);
    -webkit-mask-image: radial-gradient(circle, @innerColor @innerSize, @outerColor 100%);
  }
.gradient(@color: #F5F5F5, @start: #EEE, @stop: #FFF) {
    background: @color;
    background: -webkit-gradient(linear,
				 left bottom,
				 left top,
				 color-stop(0, @start),
				 color-stop(1, @stop));
    background: -ms-linear-gradient(bottom,
				    @start,
				    @stop);
    background: -moz-linear-gradient(center bottom,
				     @start 0%,
				     @stop 100%);
    background: -o-linear-gradient(@stop,
				   @start);
    filter: e(%("progid:DXImageTransform.Microsoft.gradient(startColorstr='%d', endColorstr='%d', GradientType=0)",@stop,@start));
}
.horizontal-gradient(@color: #F5F5F5, @start: #EEE, @stop: #FFF) {
    background: @color;
    background: -webkit-gradient(linear,
				 left top,
				 right top,
				 color-stop(0, @start),
				 color-stop(1, @stop));
    background: -ms-linear-gradient(left,
				    @start,
				    @stop);
    background: -moz-linear-gradient(left,
				     @start 0%,
				     @stop 100%);
    background: -o-linear-gradient(@stop,
				   @start);
    filter: e(%("progid:DXImageTransform.Microsoft.gradient(startColorstr='%d', endColorstr='%d', GradientType=0)",@stop,@start));
}
.bw-gradient(@color: #F5F5F5, @start: 0, @stop: 255) {
    background: @color;
    background: -webkit-gradient(linear,
				left bottom,
				left top,
				color-stop(0, rgb(@start,@start,@start)),
				color-stop(1, rgb(@stop,@stop,@stop)));
    background: -ms-linear-gradient(bottom,
				rgb(@start,@start,@start) 0%,
				rgb(@stop,@stop,@stop) 100%);
    background: -moz-linear-gradient(center bottom,
				rgb(@start,@start,@start) 0%,
				rgb(@stop,@stop,@stop) 100%);
    background: -o-linear-gradient(rgb(@stop,@stop,@stop),
				rgb(@start,@start,@start));
    filter: e(%("progid:DXImageTransform.Microsoft.gradient(startColorstr='%d', endColorstr='%d', GradientType=0)",rgb(@stop,@stop,@stop),rgb(@start,@start,@start)));
}
.bordered(@top-color: #EEE, @right-color: #EEE, @bottom-color: #EEE, @left-color: #EEE) {
    border-top: solid 1px @top-color;
    border-left: solid 1px @left-color;
    border-right: solid 1px @right-color;
    border-bottom: solid 1px @bottom-color;
}
.drop-shadow(@x-axis: 0, @y-axis: 1px, @blur: 2px, @color: rgba(0, 0, 0, 0.1)){
    -webkit-box-shadow: @x-axis @y-axis @blur @color;
    -moz-box-shadow: @x-axis @y-axis @blur @color;
    box-shadow: @x-axis @y-axis @blur @color;
}
.rounded(@radius: 2px) {
    -webkit-border-radius: @radius;
    -moz-border-radius: @radius;
    border-radius: @radius;
}
.border-radius(@topright: 0, @bottomright: 0, @bottomleft: 0, @topleft: 0) {
    -webkit-border-top-right-radius: @topright;
    -webkit-border-bottom-right-radius: @bottomright;
    -webkit-border-bottom-left-radius: @bottomleft;
    -webkit-border-top-left-radius: @topleft;
    -moz-border-radius-topright: @topright;
    -moz-border-radius-bottomright: @bottomright;
    -moz-border-radius-bottomleft: @bottomleft;
    -moz-border-radius-topleft: @topleft;
    border-top-right-radius: @topright;
    border-bottom-right-radius: @bottomright;
    border-bottom-left-radius: @bottomleft;
    border-top-left-radius: @topleft;
    .background-clip(padding-box);
}
.opacity(@opacity: 0.5) {
    -moz-opacity: @opacity;
    -khtml-opacity: @opacity;
    -webkit-opacity: @opacity;
    opacity: @opacity;
    @opperc: @opacity * 100;
    -ms-filter: ~"progid:DXImageTransform.Microsoft.Alpha(opacity=@{opperc})";
    filter: ~"alpha(opacity=@{opperc})";
}

.multipleTransitions(@value1,...){
    @value: ~`"@{arguments}".replace(/[\[\]]|\,\sX/g, '')`;
    -webkit-transition: @value;
    -moz-transition: @value;
    -ms-transition: @value;
    -o-transition: @value;
    transition: @value;
}

.transitionProperty(...) {
    -moz-transition-property: @arguments;
    -webkit-transition-property: @arguments;
    -o-transition-property: @arguments;
    transition-property: @arguments;
}

.transitionDuration(...) {
    -moz-transition-duration: @arguments;
    -webkit-transition-duration: @arguments;
    -o-transition-duration: @arguments;
    transition-duration: @arguments;
}
.transform-origin(@percX:0%, @percY:0%) {
    -webkit-transform-origin: @percX @percY;
    -moz-transform-origin: @percX @percY;
    -o-transform-origin: @percX @percY;
    -ms-transform-origin: @percX @percY;
    transform-origin:  @percX @percY;
}
.transform(...) {
    -webkit-transform: @arguments;
    -moz-transform: @arguments;
    -o-transform: @arguments;
    -ms-transform: @arguments;
    transform: @arguments;
}
.rotation(@deg:5deg){
    .transform(rotate(@deg));
}
.scale(@ratio:1.5){
    .transform(scale(@ratio));
}
.transitionDelay(...) {
    -webkit-transition-delay: @arguments;
    -moz-transition-delay: @arguments;
    -o-transition-delay: @arguments;
    transition-delay:  @arguments;
}
.transitionMore(@what:all, @duration:0.2s, @delay:0s, @ease:ease-out) {
    -webkit-transition: @what @duration @delay @ease;
    -moz-transition: @what @duration @delay @ease;
    -o-transition: @what @duration @delay @ease;
    transition: @what @duration @delay @ease;
}
.transition(@duration:0.2s, @ease:ease-out) {
    -webkit-transition: all @duration @ease;
    -moz-transition: all @duration @ease;
    -o-transition: all @duration @ease;
    transition: all @duration @ease;
}
.inner-shadow(@horizontal:0, @vertical:1px, @blur:2px, @alpha: 0.4) {
    -webkit-box-shadow: inset @horizontal @vertical @blur rgba(0, 0, 0, @alpha);
    -moz-box-shadow: inset @horizontal @vertical @blur rgba(0, 0, 0, @alpha);
    box-shadow: inset @horizontal @vertical @blur rgba(0, 0, 0, @alpha);
}
.box-shadow(@arguments) {
    -webkit-box-shadow: @arguments;
    -moz-box-shadow: @arguments;
    box-shadow: @arguments;
}
.box-sizing(@sizing: border-box) {
    -ms-box-sizing: @sizing;
    -moz-box-sizing: @sizing;
    -webkit-box-sizing: @sizing;
    box-sizing: @sizing;
}
.user-select(@argument: none) {
    -webkit-user-select: @argument;
    -moz-user-select: @argument;
    -ms-user-select: @argument;
    user-select: @argument;
}
.columns(@colwidth: 250px, @colcount: 0, @colgap: 50px, @columnRuleColor: #EEE, @columnRuleStyle: solid, @columnRuleWidth: 1px) {
    -moz-column-width: @colwidth;
    -moz-column-count: @colcount;
    -moz-column-gap: @colgap;
    -moz-column-rule-color: @columnRuleColor;
    -moz-column-rule-style: @columnRuleStyle;
    -moz-column-rule-width: @columnRuleWidth;
    -webkit-column-width: @colwidth;
    -webkit-column-count: @colcount;
    -webkit-column-gap: @colgap;
    -webkit-column-rule-color: @columnRuleColor;
    -webkit-column-rule-style: @columnRuleStyle;
    -webkit-column-rule-width: @columnRuleWidth;
    column-width: @colwidth;
    column-count: @colcount;
    column-gap: @colgap;
    column-rule-color: @columnRuleColor;
    column-rule-style: @columnRuleStyle;
    column-rule-width: @columnRuleWidth;
}
.translate(@x:0, @y:0) {
    .transform(translate(@x, @y));
}
.background-clip(@argument: padding-box) {
    -moz-background-clip: @argument;
    -webkit-background-clip: @argument;
    background-clip: @argument;
}
.hw() {
    -webkit-backface-visibility: hidden;
    -webkit-perspective: 1000;
    -webkit-transform: translate3d(0, 0, 0);
    -moz-transform: translate3d(0, 0, 0);
    -ms-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
}
.flexbox() {
    // Replaced flexbox with block/inline-block as requested
    display: block;
    width: 100%;
    &:after {
      content: "";
      display: table;
      clear: both;
    }
  }

.inline-block() {
    display: inline-block;
    vertical-align: top;
  }
.paddingRatio(@width, @height) {
    @padding-bottom: (@height / @width) * 100%;
    padding-bottom: @padding-bottom;
}
.stagger(@i, @transition, @delay: 0s) when (@i > 0) {
    &:nth-child(@{i}) {
        transition-delay: (@i * @transition + @delay);
    }
    .stagger(@i - 1, @transition, @delay);
}