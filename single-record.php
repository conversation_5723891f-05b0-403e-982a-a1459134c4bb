<?php
/**
 * The template for displaying single record
 */

get_header();

// Get record metadata
$record_id = get_post_meta(get_the_ID(), '_record_id', true);
$year = get_post_meta(get_the_ID(), '_record_year', true);
$country = get_post_meta(get_the_ID(), '_record_country', true);
$tracklist = get_post_meta(get_the_ID(), '_record_tracklist', true);
$price = get_post_meta(get_the_ID(), '_record_price', true);
$release_date = get_post_meta(get_the_ID(), '_record_release_date', true);
$media_type = get_post_meta(get_the_ID(), '_record_media_type', true);
$units = get_post_meta(get_the_ID(), '_record_units', true);
$info_line = get_post_meta(get_the_ID(), '_record_info_line', true);
?>

<section class="singleRecord" data-album="<?php the_title(); ?>">
    <div class="contentWrapper">
        <div class="recordHeader">
            <h1 class="bigTitle white"><?php the_title(); ?></h1>

            <?php
            $artists = get_the_terms(get_the_ID(), 'record_artist');
            if ($artists && !is_wp_error($artists)) :
                $artist_links = array();
                foreach ($artists as $artist) {
                    $artist_links[] = '<a href="' . esc_url(get_term_link($artist)) . '">' . esc_html($artist->name) . '</a>';
                }
            ?>
                <div class="recordArtist">
                    <?php echo implode(', ', $artist_links); ?>
                </div>
            <?php endif; ?>
        </div>

        <div class="recordContent">
            <div class="recordCoverCol">
                <div class="recordCover">
                    <?php if (has_post_thumbnail()) : ?>
                        <?php the_post_thumbnail('record-cover-optimized'); ?>
                    <?php else : ?>
                        <div class="defaultCover">
                            <svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" viewBox="0 0 100 100">
                                <circle cx="50" cy="50" r="45" fill="#1A1A1A" />
                                <circle cx="50" cy="50" r="42" fill="#333333" />
                                <circle cx="50" cy="50" r="18" fill="#F5D042" />
                                <circle cx="50" cy="50" r="3" fill="#1A1A1A" />
                            </svg>
                        </div>
                    <?php endif; ?>
                </div>

                <div class="recordMeta">
                    <?php if ($release_date) : ?>
                        <div class="metaItem">
                            <span class="metaLabel"><?php _e('Release Date:', 'rewindrecords'); ?></span>
                            <span class="metaValue"><?php echo esc_html(date_i18n(get_option('date_format'), strtotime($release_date))); ?></span>
                        </div>
                    <?php elseif ($year) : ?>
                        <div class="metaItem">
                            <span class="metaLabel"><?php _e('Year:', 'rewindrecords'); ?></span>
                            <span class="metaValue"><?php echo esc_html($year); ?></span>
                        </div>
                    <?php endif; ?>

                    <?php if ($country) : ?>
                        <div class="metaItem">
                            <span class="metaLabel"><?php _e('Country:', 'rewindrecords'); ?></span>
                            <span class="metaValue"><?php echo esc_html($country); ?></span>
                        </div>
                    <?php endif; ?>

                    <?php
                    $formats = get_the_terms(get_the_ID(), 'record_format');
                    if ($formats && !is_wp_error($formats)) :
                        $format_names = array();
                        foreach ($formats as $format) {
                            $format_names[] = $format->name;
                        }
                    ?>
                        <div class="metaItem">
                            <span class="metaLabel"><?php _e('Format:', 'rewindrecords'); ?></span>
                            <span class="metaValue"><?php echo esc_html(implode(', ', $format_names)); ?></span>
                        </div>
                    <?php endif; ?>

                    <?php
                    $genres = get_the_terms(get_the_ID(), 'record_genre');
                    if ($genres && !is_wp_error($genres)) :
                        $genre_links = array();
                        foreach ($genres as $genre) {
                            $genre_links[] = '<a href="' . esc_url(get_term_link($genre)) . '">' . esc_html($genre->name) . '</a>';
                        }
                    ?>
                        <div class="metaItem">
                            <span class="metaLabel"><?php _e('Genres:', 'rewindrecords'); ?></span>
                            <span class="metaValue"><?php echo implode(', ', $genre_links); ?></span>
                        </div>
                    <?php endif; ?>

                    <?php if ($price && rewindrecords_show_prices()) : ?>
                        <div class="metaItem">
                            <span class="metaLabel"><?php _e('Price:', 'rewindrecords'); ?></span>
                            <span class="metaValue price">€<?php echo number_format($price, 2, ',', '.'); ?></span>
                        </div>
                    <?php endif; ?>

                    <?php if ($media_type) : ?>
                        <div class="metaItem">
                            <span class="metaLabel"><?php _e('Media Type:', 'rewindrecords'); ?></span>
                            <span class="metaValue">
                                <?php
                                    echo esc_html($media_type);
                                    if ($units) {
                                        echo ' (' . esc_html($units) . ')';
                                    }
                                ?>
                            </span>
                        </div>
                    <?php endif; ?>

                    <?php if ($info_line) : ?>
                        <div class="metaItem">
                            <span class="metaLabel"><?php _e('Info:', 'rewindrecords'); ?></span>
                            <span class="metaValue"><?php echo esc_html($info_line); ?></span>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <div class="recordInfoCol">
                <?php if (!empty($tracklist)) : ?>
                    <div class="recordTracklist">
                        <h2 class="normalTitle"><?php _e('Tracklist', 'rewindrecords'); ?></h2>
                        <table class="tracklistTable">
                            <thead>
                                <tr>
                                    <th class="trackPosition"><?php _e('Nr', 'rewindrecords'); ?></th>
                                    <th class="trackTitle"><?php _e('Title', 'rewindrecords'); ?></th>
                                    <th class="trackDuration"><?php _e('Duration', 'rewindrecords'); ?></th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php
                                $current_disc = '';
                                foreach ($tracklist as $track) :
                                    // Check if this is a multi-disc album and we're starting a new disc
                                    if (isset($track['position']) && strpos($track['position'], '-') !== false) {
                                        $position_parts = explode('-', $track['position']);
                                        if (count($position_parts) > 1) {
                                            $disc = trim($position_parts[0]);
                                            if ($disc !== $current_disc) {
                                                $current_disc = $disc;
                                                echo '<tr class="disc-header"><td colspan="3"><strong>' . sprintf(__('Disc %s', 'rewindrecords'), esc_html($disc)) . '</strong></td></tr>';
                                            }
                                        }
                                    }
                                ?>
                                    <tr>
                                        <td class="trackPosition">
                                            <?php
                                            // If it's a multi-disc album, only show the track number part
                                            if (isset($track['position']) && strpos($track['position'], '-') !== false) {
                                                $position_parts = explode('-', $track['position']);
                                                if (count($position_parts) > 1) {
                                                    echo esc_html(trim($position_parts[1]));
                                                } else {
                                                    echo esc_html($track['position']);
                                                }
                                            } else {
                                                echo esc_html($track['position']);
                                            }
                                            ?>
                                        </td>
                                        <td class="trackTitle"><?php echo esc_html($track['title']); ?></td>
                                        <td class="trackDuration"><?php echo esc_html($track['duration']); ?></td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>

                <?php if (get_the_content()) : ?>
                    <div class="recordDescription">
                        <h2 class="normalTitle"><?php _e('Over dit album', 'rewindrecords'); ?></h2>
                        <div class="recordContent">
                            <?php the_content(); ?>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</section>

<?php
// Get artist names for the form
$artist_names = array();
if ($artists && !is_wp_error($artists)) {
    foreach ($artists as $artist) {
        $artist_names[] = $artist->name;
    }
}
$artist_string = implode(', ', $artist_names);
?>

<?php
// Get random records for the covers (excluding current record)
$current_record_id = get_the_ID();
$random_records_left = new WP_Query(array(
    'post_type' => 'record',
    'posts_per_page' => 2,
    'orderby' => 'rand',
    'post__not_in' => array($current_record_id),
    'tax_query' => array(
        'relation' => 'AND',
        // Only show albums
        array(
            'taxonomy' => 'record_product_type',
            'field' => 'slug',
            'terms' => 'album',
        ),
        // Exclude records with format 'Spellen' or other non-music formats
        array(
            'taxonomy' => 'record_format',
            'field' => 'slug',
            'terms' => array('spellen', 'bordspellen', 'games', 'board-games'),
            'operator' => 'NOT IN',
        ),
    ),
));

$random_records_right = new WP_Query(array(
    'post_type' => 'record',
    'posts_per_page' => 2,
    'orderby' => 'rand',
    'post__not_in' => array_merge(array($current_record_id), wp_list_pluck($random_records_left->posts, 'ID')),
    'tax_query' => array(
        'relation' => 'AND',
        // Only show albums
        array(
            'taxonomy' => 'record_product_type',
            'field' => 'slug',
            'terms' => 'album',
        ),
        // Exclude records with format 'Spellen' or other non-music formats
        array(
            'taxonomy' => 'record_format',
            'field' => 'slug',
            'terms' => array('spellen', 'bordspellen', 'games', 'board-games'),
            'operator' => 'NOT IN',
        ),
    ),
));
?>

<section class="contactBlock" data-init>
    <div class="cols">
        <div class="col" data-parallax data-parallax-speed="-1">
            <div class="images">
                <?php if ($random_records_left->have_posts()) : $i = 1; while ($random_records_left->have_posts() && $i <= 2) : $random_records_left->the_post(); ?>
                    <div class="imageWrapper">
                        <div class="innerImage">
                            <?php if (has_post_thumbnail()) : ?>
                                <a href="<?php the_permalink(); ?>" title="<?php the_title_attribute(); ?>">
                                    <img class="lazy" data-src="<?php echo esc_url(get_the_post_thumbnail_url(get_the_ID(), 'large')); ?>" alt="<?php the_title_attribute(); ?>" />
                                </a>
                            <?php else : ?>
                                <a href="<?php the_permalink(); ?>" title="<?php the_title_attribute(); ?>">
                                    <div class="defaultCover">
                                        <svg viewBox="0 0 512 512" xmlns="http://www.w3.org/2000/svg">
                                            <circle cx="256" cy="256" r="240" fill="#333" />
                                            <circle cx="256" cy="256" r="120" fill="#F5D042" />
                                            <circle cx="256" cy="256" r="30" fill="#333" />
                                        </svg>
                                    </div>
                                </a>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php $i++; endwhile; wp_reset_postdata(); endif; ?>
            </div>
        </div>

        <div class="col">
            <div class="formWrapper">
                <div class="formIntro">
                    <h2 class="normalTitle" data-lines data-words><?php _e('Interesse in dit album?', 'rewindrecords'); ?></h2>
                    <div class="text"><p data-lines data-words><?php _e('Heb je een vraag over dit album of wil je het reserveren? Vul het formulier in en we nemen zo snel mogelijk contact met je op.', 'rewindrecords'); ?></p></div>
                </div>
                <div class="contactFormWrapper" data-swup-form>
                    <?php
                    // Restore the current post
                    wp_reset_postdata();

                    // Get the album title and artist for the form
                    $album_title = get_the_title($current_record_id);

                    // Use Contact Form 7's filter to pre-fill the hidden fields
                    add_filter('wpcf7_form_tag_data_option', function($options, $tag) use ($album_title, $artist_string) {
                        if ($tag['name'] == 'album-title') {
                            $options = array(esc_attr($album_title));
                        } elseif ($tag['name'] == 'album-artist') {
                            $options = array(esc_attr($artist_string));
                        }
                        return $options;
                    }, 10, 2);

                    // Output the Contact Form 7 shortcode
                    echo do_shortcode('[contact-form-7 id="e0636cc" title="Album Inquiry"]');
                    ?>
                </div>
            </div>
        </div>

        <div class="col" data-parallax data-parallax-speed="-1">
            <div class="images">
                <?php if ($random_records_right->have_posts()) : $i = 1; while ($random_records_right->have_posts() && $i <= 2) : $random_records_right->the_post(); ?>
                    <div class="imageWrapper">
                        <div class="innerImage">
                            <?php if (has_post_thumbnail()) : ?>
                                <a href="<?php the_permalink(); ?>" title="<?php the_title_attribute(); ?>">
                                    <img class="lazy" data-src="<?php echo esc_url(get_the_post_thumbnail_url(get_the_ID(), 'large')); ?>" alt="<?php the_title_attribute(); ?>" />
                                </a>
                            <?php else : ?>
                                <a href="<?php the_permalink(); ?>" title="<?php the_title_attribute(); ?>">
                                    <div class="defaultCover">
                                        <svg viewBox="0 0 512 512" xmlns="http://www.w3.org/2000/svg">
                                            <circle cx="256" cy="256" r="240" fill="#333" />
                                            <circle cx="256" cy="256" r="120" fill="#F5D042" />
                                            <circle cx="256" cy="256" r="30" fill="#333" />
                                        </svg>
                                    </div>
                                </a>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php $i++; endwhile; wp_reset_postdata(); endif; ?>
            </div>
        </div>
    </div>
</section>

<?php
get_footer();
