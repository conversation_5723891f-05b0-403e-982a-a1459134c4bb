<!DOCTYPE html>
<html lang="nl" dir="ltr">
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
  <title><?php echo get_theme_mod('customTheme-main-callout-title') ? get_theme_mod('customTheme-main-callout-title') : get_bloginfo('name'); ?> | <?php the_title(); ?></title>
  <meta name="robots" content="follow, index, max-snippet:-1, max-video-preview:-1, max-image-preview:large">
  <meta name="msapplication-TileColor" content="#00aba9">
  <meta name="theme-color" content="#ffffff">
  <meta name="description" content="<?php echo esc_attr(get_theme_mod('customTheme-main-callout-description')); ?>">
  <meta name="author" content="https://www.linkedin.com/in/dennisthemenace/"/>
  <meta property="og:title" content="<?php echo get_theme_mod('customTheme-main-callout-title') ? get_theme_mod('customTheme-main-callout-title') : get_bloginfo('name'); ?> | <?php the_title(); ?>" />
  <meta property="og:description" content="<?php echo esc_attr(get_theme_mod('customTheme-main-callout-description')); ?>" />
  <meta property="og:image" content="<?php echo esc_url(wp_get_attachment_url(get_theme_mod('customTheme-main-callout-featured-image'))); ?>" />
  <meta property="og:image:alt" content="<?php echo esc_attr(get_theme_mod('customTheme-main-callout-title')); ?>" />
  <meta property="og:image:width" content="1200" />
  <meta property="og:image:height" content="630" />
  <meta property="og:image:type" content="image/jpeg" />
  <meta property="og:type" content="website" />
  <meta property="og:url" content="<?php echo esc_url(get_permalink()); ?>" />
  <meta property="og:site_name" content="<?php echo get_theme_mod('customTheme-main-callout-title') ? get_theme_mod('customTheme-main-callout-title') : get_bloginfo('name'); ?>" />
  <meta property="og:locale" content="nl" />
  <meta name="DC.title" content="<?php echo the_title(); ?>">
  <meta name="DC.creator" content="Door Dennis">
  <meta name="DC.subject" content="<?php echo esc_attr(get_theme_mod('customTheme-main-callout-description')); ?>">
  <meta name="DC.description" content="<?php echo esc_attr(get_theme_mod('customTheme-main-callout-description')); ?>">
  <meta name="DC.publisher" content="<?php echo get_bloginfo('name'); ?>">
  <meta name="DC.language" content="nl">
  <meta name="twitter:card" content="summary_large_image">
  <meta name="twitter:title" content="<?php echo get_theme_mod('customTheme-main-callout-title') ? get_theme_mod('customTheme-main-callout-title') : get_bloginfo('name'); ?> | <?php the_title(); ?>">
  <meta name="twitter:description" content="<?php echo esc_attr(get_theme_mod('customTheme-main-callout-description')); ?>">
  <meta name="twitter:image" content="<?php echo esc_url(wp_get_attachment_url(get_theme_mod('customTheme-main-callout-featured-image'))); ?>">
  <link rel="canonical" href="<?php echo esc_url(get_permalink()); ?>" />
  <link rel="icon" href="<?php echo get_stylesheet_directory_uri(); ?>/favicon.ico" type="image/x-icon">
  <link rel="shortcut icon" href="<?php echo get_stylesheet_directory_uri(); ?>/favicon.ico" type="image/x-icon">
  <link rel="stylesheet" href="https://use.typekit.net/sui0kvj.css">
  <script type="application/ld+json">
  {
    "@context": "https://schema.org",
    "@type": "Organization",
    "name": "<?php echo get_bloginfo('name'); ?>",
    "url": "<?php echo esc_url(home_url()); ?>",
    "logo": "<?php echo get_stylesheet_directory_uri(); ?>/logo.png",
    "description": "<?php echo esc_html(get_theme_mod('customTheme-main-callout-description')); ?>",
    "image": "<?php echo esc_url(get_theme_mod('customTheme-main-callout-featured-image')); ?>",
    "contactPoint": {
      "@type": "ContactPoint",
      "telephone": "<?php echo esc_html(get_theme_mod('customTheme-main-callout-telephone')); ?>",
      "contactType": "customer service",
      "contactOption": "TollFree",
      "areaServed": "NL",
      "availableLanguage": "Dutch"
    },
    "address": {
      "@type": "PostalAddress",
      "streetAddress": "<?php echo esc_html(get_theme_mod('customTheme-main-callout-street')); ?>",
      "addressLocality": "<?php echo esc_html(get_theme_mod('customTheme-main-callout-city')); ?>"
    },
    "sameAs": [
      "<?php echo esc_url(get_theme_mod('customTheme-main-callout-facebook')); ?>",
      "<?php echo esc_url(get_theme_mod('customTheme-main-callout-linkedin')); ?>",
      "<?php echo esc_url(get_theme_mod('customTheme-main-callout-facebook')); ?>",
      "<?php echo esc_url(get_theme_mod('customTheme-main-callout-instagram')); ?>"
    ],
    "email": "<?php echo esc_html(get_theme_mod('customTheme-main-callout-mail')); ?>",
    "telephone": "<?php echo esc_html(get_theme_mod('customTheme-main-callout-telephone')); ?>",
    "additionalProperty": [
      {
        "@type": "PropertyValue",
        "name": "Company Information",
        "value": "<?php echo esc_html(get_theme_mod('customTheme-main-callout-company-information')); ?>"
      },
      {
        "@type": "PropertyValue",
        "name": "Analytics ID",
        "value": "<?php echo esc_html(get_theme_mod('customTheme-main-callout-analytics')); ?>"
      }
    ]
  }
  </script>

  <script>
    window.dataLayer = window.dataLayer || [];
    function gtag(){dataLayer.push(arguments);}
    gtag('js', new Date());

    gtag('config', '<?php echo get_theme_mod('customTheme-main-callout-analytics') ?>', {
      'anonymize_ip': true
    });
  </script>

  <?php wp_head(); ?>
</head>
<?php if(get_theme_mod("customTheme-main-callout-analytics")) { ?>
  <!-- Google tag (gtag.js) -->
  <script async src="https://www.googletagmanager.com/gtag/js?id=<?php echo get_theme_mod('customTheme-main-callout-analytics') ?>"></script>

<?php } ?>
  <body class="no-scroll">
    <header>
      <div class="background"></div>
      <div class="contentWrapper">
        <div class="col">
            <a href="/" title="Logo | <?php echo get_theme_mod('customTheme-main-callout-title') ?>" class="logo">
            <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="135.273" height="135.838" viewBox="0 0 135.273 135.838">
                <defs>
                  <clipPath id="clip-path">
                    <rect id="Rectangle_235" data-name="Rectangle 235" width="135.273" height="135.838" transform="translate(0 0)" fill="#eee"/>
                  </clipPath>
                </defs>
                <g id="Group_223" data-name="Group 223" transform="translate(0 -0.004)">
                  <g id="Group_223-2" data-name="Group 223" transform="translate(0 0.004)" clip-path="url(#clip-path)">
                    <path id="Path_1994" data-name="Path 1994" d="M427.2,190.293a34.031,34.031,0,0,1-12.618-2.109,36.35,36.35,0,0,1-6.573-3.17,35.743,35.743,0,0,1-16.327-37.462A34.778,34.778,0,0,1,397,134.722c.993-1.464,2.159-2.812,3.267-4.2.4-.5.494-.49,1.006-.129q3.853,2.716,7.7,5.442c.311.22.312.373-.008.68a24.348,24.348,0,0,0-3.546,4.332,25.392,25.392,0,0,0-3.779,17.656,24.933,24.933,0,0,0,2.523,7.734,25.608,25.608,0,0,0,5.09,6.8,25.646,25.646,0,0,0,18.86,7.006,28.024,28.024,0,0,0,5.485-.9,25.394,25.394,0,0,0,18.637-23.019,25.477,25.477,0,0,0-1.606-10.531,25.477,25.477,0,0,0-12.515-13.786,23.718,23.718,0,0,0-5.457-1.947,10.978,10.978,0,0,0-1.112-.231c-.4-.046-.526-.242-.523-.62.011-1.275,0-2.549,0-3.824q0-2.787,0-5.574c0-.511.086-.62.6-.538,1.083.172,2.173.328,3.234.593,1.39.348,2.772.747,4.129,1.209a33.438,33.438,0,0,1,7.406,3.673,35.752,35.752,0,0,1,7.11,53.866,35.95,35.95,0,0,1-10.009,7.81,34.167,34.167,0,0,1-6.185,2.553,33.282,33.282,0,0,1-10.109,1.5" transform="translate(-359.168 -109.371)" fill="#eee"/>
                    <path id="Path_1995" data-name="Path 1995" d="M59.96,9.2a3.278,3.278,0,0,1-.376.408c-1.224.873-2.442,1.756-3.691,2.594a2.007,2.007,0,0,1-1.047.331c-5.167.022-10.334.019-15.5.018q-12.531,0-25.062,0A10.681,10.681,0,0,0,3.444,23.6q.032,25.021.009,50.043,0,28.175-.006,56.349a10.872,10.872,0,0,0,1.1,5.031,10.554,10.554,0,0,0,4.935,4.827,10.687,10.687,0,0,0,4.723,1.029q12.735-.009,25.469,0,40.767,0,81.533-.013a10.252,10.252,0,0,0,9.331-5.491,9.645,9.645,0,0,0,1.286-4.934q0-53.785-.017-107.571a9.962,9.962,0,0,0-4.8-8.568,11.374,11.374,0,0,0-6.57-1.751q-23.435.029-46.87.01c-.637,0-1.275-.009-1.912,0-.374,0-.538-.155-.526-.543.02-.677.008-1.356.007-2.034,0-.771.04-.814.795-.814H84.47q18.451,0,36.9.011a13.391,13.391,0,0,1,5.328,1.14,13.972,13.972,0,0,1,6.2,5.133,13.008,13.008,0,0,1,2.3,6.716c.041.88.068,1.761.068,2.641q.005,51.874,0,103.748c0,1.043-.037,2.087-.09,3.129a11.968,11.968,0,0,1-1.128,4.259,13.857,13.857,0,0,1-4.95,5.9,13.553,13.553,0,0,1-7.8,2.411q-53.725,0-107.45-.005a13.354,13.354,0,0,1-7.3-2.174,14.333,14.333,0,0,1-4.16-4.05,11.687,11.687,0,0,1-1.94-4.437,22.559,22.559,0,0,1-.442-4.108Q-.016,76.522,0,23.57A14.047,14.047,0,0,1,4.332,13.131a13.187,13.187,0,0,1,5.886-3.392,15.219,15.219,0,0,1,3.806-.555q22.662-.057,45.323-.024c.158,0,.316.022.612.044" transform="translate(0 -8.412)" fill="#eee"/>
                    <path id="Path_1996" data-name="Path 1996" d="M567.35,1077.2c.051-.422.109-.843.151-1.265.428-4.3.844-8.608,1.285-12.909.133-1.294.344-2.58.5-3.873.05-.42.239-.578.662-.575,2.1.014,4.2,0,6.306.007.545,0,.588.061.534.585q-.815,7.828-1.629,15.657c-.349,3.331-.714,6.659-1.068,9.99-.089.836-.155,1.674-.242,2.51-.053.5-.166.6-.679.6q-4.313,0-8.625,0c-.591,0-.7-.075-.785-.64q-.963-6.208-1.913-12.418a4.2,4.2,0,0,0-.271-1.065,4.448,4.448,0,0,0-.211.687c-.414,2.94-.816,5.882-1.226,8.822-.187,1.343-.382,2.684-.577,4.026-.07.482-.177.583-.659.584-2.794,0-5.587,0-8.381.006a.55.55,0,0,1-.635-.566c-.347-3.057-.709-6.113-1.065-9.17q-.652-5.595-1.3-11.19c-.3-2.571-.62-5.141-.927-7.712-.078-.649-.027-.705.627-.706q3.072,0,6.143,0c.609,0,.641.038.687.63q.527,6.813,1.057,13.626c.1,1.21.221,2.417.333,3.626l.151.012c.066-.414.152-.825.194-1.24.258-2.563.479-5.129.763-7.689.305-2.748.663-5.491,1.005-8.235.091-.731.1-.731.827-.733l6.469-.008c.742,0,.781.014.845.748q.416,4.761.808,9.524c.212,2.553.411,5.107.621,7.66.019.234.078.465.118.7l.11,0" transform="translate(-502.088 -972.437)" fill="#eee"/>
                    <path id="Path_1997" data-name="Path 1997" d="M1315.55,1076.352v-11.026c0-.546.078-.633.641-.634q3.234-.007,6.469,0c.479,0,.585.112.587.615,0,1.058,0,2.115,0,3.173q0,6.876,0,13.751c0,.693.034.725.707.661.962-.092,1.4-.49,1.473-1.356.064-.8.178-1.593.182-2.39q.027-6.2,0-12.409a15.777,15.777,0,0,0-.178-2.02,1.023,1.023,0,0,0-.965-.9,10.841,10.841,0,0,0-1.453-.135c-2.265-.016-4.53-.008-6.794-.012-.626,0-.663-.044-.664-.674,0-1.261.008-2.522-.006-3.783,0-.374.123-.555.511-.551,3.4.036,6.807.026,10.209.122a9.523,9.523,0,0,1,3.534.671,5.039,5.039,0,0,1,3.252,4.133,29.762,29.762,0,0,1,.224,3.814q.04,6.2,0,12.409a36.489,36.489,0,0,1-.245,4.219,3.828,3.828,0,0,1-2.447,3.245,7.62,7.62,0,0,1-2.1.543c-1.333.127-2.678.168-4.019.181-2.726.025-5.452.008-8.178.007-.688,0-.73-.041-.73-.745q0-5.452,0-10.9Z" transform="translate(-1208.499 -972.514)" fill="#eee"/>
                    <path id="Path_1998" data-name="Path 1998" d="M1074.577,1075.686a4.511,4.511,0,0,0-.07.563q-.008,4.189-.006,8.379a.933.933,0,0,1-.018.4c-.059.11-.255.266-.318.239-.981-.44-2.01-.157-3.014-.228a1.65,1.65,0,0,0-1.324.385,1.585,1.585,0,0,0-.241,1.972c.094.155.174.317.306.557-.59,0-1.084.014-1.575-.013-.089-.005-.2-.168-.244-.281a.989.989,0,0,1-.022-.362q0-13.952,0-27.9c0-.727.02-.747.752-.748q2.5,0,5,0c.719,0,.727,0,.956.72.542,1.69,1.075,3.384,1.621,5.073q1,3.093,2.016,6.182c.029.088.072.17.109.256l.114,0a6.959,6.959,0,0,0,.09-.79q.011-4.21.006-8.42c0-.841.013-1.682-.007-2.522-.009-.379.154-.505.506-.5,1.817.005,3.634,0,5.451.006.458,0,.5.062.5.622q0,11.206,0,22.413,0,2.807,0,5.613c0,.635-.047.688-.658.689-1.83,0-3.661-.01-5.491.007a.7.7,0,0,1-.783-.586c-.65-2.223-1.316-4.442-1.979-6.662q-.7-2.338-1.4-4.673c-.04-.134-.1-.262-.151-.393l-.128.006" transform="translate(-981.139 -972.5)" fill="#eee"/>
                    <path id="Path_1999" data-name="Path 1999" d="M142.353,1072.677c.548.23,1.006.39,1.433.609a3.8,3.8,0,0,1,2.236,3.382c.077,1.977.088,3.957.108,5.935.016,1.573,0,3.146,0,4.719,0,.546-.093.645-.635.646q-2.97,0-5.939,0c-.506,0-.579-.075-.579-.6,0-3.051.011-6.1-.007-9.152A17.1,17.1,0,0,0,138.8,1076c-.107-.774-.388-1.015-1.149-1.15-.912-.163-.949-.13-.948.785q0,5.858,0,11.715c0,.546-.066.621-.6.622q-3.275.006-6.549,0c-.49,0-.56-.074-.56-.574q0-11.085,0-22.169c0-.485.077-.565.572-.565q3.275,0,6.549,0c.532,0,.6.082.6.624,0,1.424-.011,2.847-.012,4.271,0,.495.122.605.609.544a4.059,4.059,0,0,0,.717-.14.965.965,0,0,0,.731-.7,11.517,11.517,0,0,0,.03-4.6,1.045,1.045,0,0,0-.852-.836,6.793,6.793,0,0,0-1.365-.185c-2.318-.017-4.637-.006-6.956-.006-.574,0-.623-.046-.624-.625,0-1.288.01-2.576-.007-3.864-.005-.39.142-.53.522-.527,3.186.026,6.373,0,9.558.083a12.628,12.628,0,0,1,3.28.491,4.635,4.635,0,0,1,3.134,3.116,14.483,14.483,0,0,1,.245,7.864,2.839,2.839,0,0,1-2.158,2.149c-.372.113-.748.214-1.222.348" transform="translate(-118.497 -972.484)" fill="#eee"/>
                    <path id="Path_2000" data-name="Path 2000" d="M375.48,1073.318q0-7,0-14c0-.62.025-.647.636-.648q5.716,0,11.432,0c.63,0,.671.045.672.67q0,2.278,0,4.557c0,.57-.1.666-.663.668-1.275,0-2.55,0-3.824,0-.549,0-.588.045-.588.573q0,2.136,0,4.272c0,.57.059.63.629.631,1.166,0,2.332,0,3.5,0,.558,0,.63.076.632.64q.006,2.2,0,4.394c0,.554-.071.622-.643.624-1.18,0-2.36,0-3.539,0-.5,0-.57.06-.572.559-.009,1.776,0,3.553-.011,5.329,0,.379.164.531.543.528,1.465-.01,2.929-.005,4.394,0,.625,0,.666.042.667.67q0,2.3,0,4.6c0,.522-.094.619-.618.619q-6.021,0-12.042,0c-.541,0-.606-.076-.606-.66q0-7.018,0-14.036" transform="translate(-344.927 -972.529)" fill="#eee"/>
                    <path id="Path_2001" data-name="Path 2001" d="M717.942,431.475A10.028,10.028,0,1,1,707.9,441.529a10.025,10.025,0,0,1,10.042-10.054m-2.579,10a2.586,2.586,0,1,0,5.172.009,2.586,2.586,0,0,0-5.172-.009" transform="translate(-650.297 -396.365)" fill="#eee"/>
                    <path id="Path_2002" data-name="Path 2002" d="M619.626,13.192q0,6.143,0,12.285a1.921,1.921,0,0,1,0,.325c-.031.178-.083.353-.125.529a2.546,2.546,0,0,1-.506-.213q-2.71-1.9-5.409-3.82l-7.924-5.611q-2.189-1.55-4.376-3.1c-.539-.383-.548-.43,0-.817q3.757-2.672,7.524-5.331l9.851-6.97c.155-.11.3-.229.461-.333.344-.228.5-.156.509.246.012.379.005.759.005,1.139q0,5.837,0,11.675" transform="translate(-551.979 -0.004)" fill="#eee"/>
                    <path id="Path_2003" data-name="Path 2003" d="M936.534,1073.259q0-6.936,0-13.872c0-.7.03-.727.74-.728q3.132,0,6.265,0c.611,0,.66.052.66.639q0,13.872,0,27.745c0,.957,0,.957-.974.957q-2.99,0-5.98,0c-.625,0-.707-.086-.707-.7q0-7.018,0-14.035Z" transform="translate(-860.327 -972.514)" fill="#eee"/>
                    <path id="Path_2004" data-name="Path 2004" d="M1044.777,1396.168a18.521,18.521,0,0,0,.012,2.032,57.279,57.279,0,0,1,.2,6.318c0,.4.234.458.557.471a11.353,11.353,0,0,1,1.327.106.676.676,0,0,1,.629.6,1.051,1.051,0,0,1-.391,1.012,1.023,1.023,0,0,1-.425.231c-1.143.185-2.3.373-3.3-.446a.555.555,0,0,0-.792-.014c-1.726,1.291-3.349.825-4.788-.318a5.159,5.159,0,0,1-1.765-2.72,9.412,9.412,0,0,1-.138-1.325,8.63,8.63,0,0,1,.68-3.923,1.071,1.071,0,0,1,.214-.389c.572-.6,1.147-1.19,1.743-1.762a1.076,1.076,0,0,1,.516-.221,3.832,3.832,0,0,1,3.184.5c.463.335.727.224.767-.354.038-.54.028-1.083.03-1.625a.477.477,0,0,0-.446-.531c-.388-.059-.778-.117-1.162-.2a1.067,1.067,0,0,1-.906-.773.729.729,0,0,1,.66-.96c.866-.031,1.735-.035,2.6-.007a1.161,1.161,0,0,1,.989,1.2c.011,1.03,0,2.061,0,3.091m-7,5.157a4.756,4.756,0,0,0,1.041,3.09,2.032,2.032,0,0,0,3.359-.181,4.951,4.951,0,0,0,.682-4.117,3.6,3.6,0,0,0-1.21-2.019,2.025,2.025,0,0,0-2.822.146,4.525,4.525,0,0,0-1.049,3.08" transform="translate(-951.574 -1278.597)" fill="#eee"/>
                    <path id="Path_2005" data-name="Path 2005" d="M1192.854,1440.712c0,.42.014.841-.006,1.26a1.416,1.416,0,0,1-.141.582.759.759,0,0,1-1.394.085c-.284-.425-.5-.9-.739-1.351a1.445,1.445,0,0,0-1.186-.817c-.548-.048-1.1-.06-1.651-.072a1.371,1.371,0,0,0-1.255.827,1.053,1.053,0,0,0,.975,1.536c.97.077,1.94.174,2.9.306a4.467,4.467,0,0,1,1.205.346,3.225,3.225,0,0,1,1.436,4.422,4.247,4.247,0,0,1-5.421,1.856,3.5,3.5,0,0,1-.687-.5c-.437-.326-.562-.289-.753.216-.113.3-.25.567-.618.629-.559.095-.858-.078-.99-.628a2.4,2.4,0,0,1-.069-.643c.037-.972.1-1.943.135-2.914a.728.728,0,0,1,.875-.8c.359.019.483.24.622.535.844,1.785,1.921,2.644,3.665,2.319a2.434,2.434,0,0,0,1.449-.755,1.155,1.155,0,0,0-.678-2.031c-1.133-.2-2.276-.34-3.411-.529a2.847,2.847,0,0,1-2.163-1.343,3.286,3.286,0,0,1,1.618-4.609,4.891,4.891,0,0,1,3.949.4c.369.155.607.21.86-.159a.716.716,0,0,1,.888-.3.761.761,0,0,1,.559.8c0,.447,0,.895,0,1.342h.025" transform="translate(-1088.079 -1321.324)" fill="#eee"/>
                    <path id="Path_2006" data-name="Path 2006" d="M536.488,1448.243c-1.083,0-2.167-.019-3.249.008-.579.014-.67.16-.618.742a2.893,2.893,0,0,0,3.229,2.655,2.781,2.781,0,0,0,2.063-1.228,3.773,3.773,0,0,1,.8-.85.78.78,0,0,1,.918-.056.693.693,0,0,1,.34.766,3.422,3.422,0,0,1-1.368,2.269c-1.826,1.418-5.1,1.465-6.726-.707a5.962,5.962,0,0,1-1.166-3.976,7.172,7.172,0,0,1,1-3.6,4.268,4.268,0,0,1,6.559-.958,5.865,5.865,0,0,1,1.9,3.739c.094.588-.26,1-.957,1.06-.727.063-1.461.044-2.192.059-.176,0-.352,0-.529,0Zm-1.159-1.769c.677,0,1.355.007,2.032,0,.527-.007.768-.256.6-.745a1.919,1.919,0,0,0-.67-.961,3.369,3.369,0,0,0-3.3-.211,2.045,2.045,0,0,0-1.136,1.214c-.172.478-.027.695.485.7.664.01,1.327,0,1.991,0" transform="translate(-487.512 -1324.829)" fill="#eee"/>
                    <path id="Path_2007" data-name="Path 2007" d="M899.759,1445.232c-.043-.785-.091-1.57-.127-2.355a.5.5,0,0,0-.558-.547,10.989,10.989,0,0,1-1.5-.1,1.069,1.069,0,0,1-.88-1.51.825.825,0,0,1,.8-.586c.93.007,1.861.048,2.791.091a1.134,1.134,0,0,1,1.1.807c.164.447.336.471.739.193a5.219,5.219,0,0,1,2.159-1.073,2.723,2.723,0,0,1,3.111,1.628,2.634,2.634,0,0,1-.128,2.02,1.493,1.493,0,0,1-1.388.755,1.38,1.38,0,0,1-1.178-1.092,6.567,6.567,0,0,1-.193-1c-.069-.422-.368-.592-.674-.311a7.821,7.821,0,0,0-2.111,2.442.835.835,0,0,0-.087.351c.047,1.326.095,2.652.162,3.977.019.385.328.471.646.47.352,0,.706-.067,1.055-.045.912.057,1.388.7,1.116,1.47a1.161,1.161,0,0,1-.987.742,5.336,5.336,0,0,1-.932.069c-1.436.008-2.872.026-4.307-.005a2.775,2.775,0,0,1-1.113-.287,1.037,1.037,0,0,1-.494-1.153.8.8,0,0,1,.729-.759,4.694,4.694,0,0,1,1.243,0c.692.088.842,0,.878-.668.064-1.175.117-2.351.174-3.527l-.048,0" transform="translate(-823.647 -1322.931)" fill="#eee"/>
                    <path id="Path_2008" data-name="Path 2008" d="M396.746,1451.591c-1.233,0-2.466.021-3.7-.013a2.6,2.6,0,0,1-1.112-.285,1.042,1.042,0,0,1-.472-1.268,1,1,0,0,1,1.013-.726,4.953,4.953,0,0,1,.927.084c.56.086.856-.016.919-.706a47.962,47.962,0,0,0-.02-5.809c-.011-.376-.177-.579-.568-.588-.434-.009-.869-.014-1.3-.055-.769-.074-1.206-.529-1.175-1.187a.989.989,0,0,1,1.115-.972c.336.018.674-.012,1.01.009.62.038,1.242.073,1.857.157a1.046,1.046,0,0,1,.8.736c.2.479.325.5.749.2a5.2,5.2,0,0,1,2.079-1.057,2.838,2.838,0,0,1,3.1,1.412,2.5,2.5,0,0,1,.04,2.054,1.53,1.53,0,0,1-1.486.937,1.48,1.48,0,0,1-1.2-1.228c-.074-.287-.095-.588-.148-.881-.074-.4-.416-.562-.71-.28a8.676,8.676,0,0,0-2.108,2.45.939.939,0,0,0-.124.382q.023,1.339.079,2.677c.015.365.073.727.1,1.091.039.456.175.6.628.622.215.008.434-.05.648-.035a5.232,5.232,0,0,1,1,.134.815.815,0,0,1,.638.8,1.021,1.021,0,0,1-.5,1.069,4.437,4.437,0,0,1-2.069.281" transform="translate(-359.421 -1322.879)" fill="#eee"/>
                    <path id="Path_2009" data-name="Path 2009" d="M598.061,387.106a19.2,19.2,0,0,1,.386-4.684,18.644,18.644,0,0,1,3.229-7.131,30.475,30.475,0,0,1,2.3-2.621c.383-.411.5-.385.974-.054.8.556.81.565.124,1.227a17.71,17.71,0,0,0-3.934,5.528,17.5,17.5,0,0,0-1.511,6.164,17.074,17.074,0,0,0,2.021,9.091,15.987,15.987,0,0,0,4.376,5.284c.832.643,1.728,1.207,2.611,1.782a.664.664,0,0,1,.363.622c-.007.271.007.542,0,.813-.011.4-.158.485-.514.307a19.039,19.039,0,0,1-4.246-2.9,18.529,18.529,0,0,1-4.631-6.41,17.792,17.792,0,0,1-1.551-6.69c0-.108,0-.217,0-.325" transform="translate(-549.386 -342.065)" fill="#eee"/>
                    <path id="Path_2010" data-name="Path 2010" d="M768.07,1443.885a8.008,8.008,0,0,1,.931-3.837,4.724,4.724,0,0,1,7.317-1.089,4.035,4.035,0,0,1,.725,1.065,14.719,14.719,0,0,1,.721,1.5,4.07,4.07,0,0,1,.221,2.778,8.732,8.732,0,0,1-1.385,3.521,5.318,5.318,0,0,1-2.232,1.711,2.4,2.4,0,0,1-1.9-.051,1.685,1.685,0,0,0-.459-.153,4.075,4.075,0,0,1-3.409-2.875,8.19,8.19,0,0,1-.531-2.572m5.055,3.284c1.548.177,2.225-.669,2.656-1.877a5.916,5.916,0,0,0,0-3.643,2.013,2.013,0,0,0-1.3-1.486c-.14-.05-.281-.1-.418-.158a2.569,2.569,0,0,0-3.463,1.2,5.007,5.007,0,0,0-.553,3.539,2.645,2.645,0,0,0,2.8,2.422h.285" transform="translate(-705.572 -1320.698)" fill="#eee"/>
                    <path id="Path_2011" data-name="Path 2011" d="M653.465,1442.8c.036.418.063.837.109,1.253a3.167,3.167,0,0,0,.857,1.576c.876,1.12,2.058.91,3.2.744.3-.043.582-.414.8-.691.275-.348.464-.763.711-1.135a.826.826,0,0,1,1.027-.433.984.984,0,0,1,.46,1,3.61,3.61,0,0,1-1.819,2.827,4.01,4.01,0,0,1-2.07.762,4.433,4.433,0,0,1-3.7-1.7,6.276,6.276,0,0,1-.935-6.753,5.523,5.523,0,0,1,1.659-2.366,3.971,3.971,0,0,1,2.958-.877,4.307,4.307,0,0,1,3.679,2.559,2.2,2.2,0,0,1,.089,1.667,1.466,1.466,0,0,1-1.873,1.044,1.626,1.626,0,0,1-1.04-1.888c.253-1.145.117-1.286-1.039-1.336a2.94,2.94,0,0,0-2.982,2.648c-.025.188-.046.377-.056.566s0,.352,0,.528l-.038,0" transform="translate(-598.527 -1320.059)" fill="#eee"/>
                    <path id="Path_2012" data-name="Path 2012" d="M980.525,438.064a18.834,18.834,0,0,1-4.4,12.582c-.521.638-1.129,1.208-1.712,1.793-.344.345-.477.329-.8-.023-.86-.937-.685-.749.037-1.466a17.276,17.276,0,0,0,5.232-11.25,16.494,16.494,0,0,0-1.22-7.639c-.278-.675-.6-1.334-.931-1.985-.146-.287-.134-.477.157-.631.918-.485.942-.781,1.537.375a19.074,19.074,0,0,1,2.1,8.245" transform="translate(-893.859 -394.107)" fill="#eee"/>
                  </g>
                </g>
              </svg>
            </a>
          <div class="innerMenu">
              <?php wp_nav_menu( array(
                'menu' => 'primary-menu',
                'container' => false,
                'menu_class' => 'link',
              ) ); ?>
          </div>
        </div>
        <div class="col">
          <div class="socials">
            <?php if(get_theme_mod('customTheme-main-callout-telephone')): ?><a class="social" title="<?php echo get_theme_mod('customTheme-main-callout-telephone-label') ?>" href="tel:<?php echo get_theme_mod('customTheme-main-callout-telephone') ?>" target="_blank"><i class="icon-phone"></i></a><?php endif; ?>
            <?php if (get_theme_mod('customTheme-main-callout-mail')): ?><a class="social" title="<?php echo get_theme_mod('customTheme-main-callout-mail') ?>" href="mailto:<?php echo get_theme_mod('customTheme-main-callout-mail') ?>" target="_blank"><i class="icon-mail"></i></a><?php endif; ?>
            <?php if (get_theme_mod('customTheme-main-callout-instagram')): ?><a class="social" href="<?php echo esc_url(get_theme_mod('customTheme-main-callout-instagram', 'https://www.instagram.com')); ?>" title="instagram" target="_blank"><i class="icon-insta"></i></a><?php endif; ?>
            <?php if (get_theme_mod('customTheme-main-callout-facebook')): ?><a class="social" href="<?php echo esc_url(get_theme_mod('customTheme-main-callout-facebook', 'https://www.facebook.com')); ?>" title="facebook" target="_blank"><i class="icon-facebook"></i></a><?php endif; ?>
          </div>
          <div id="menu" class="mobileMenu">
            <div class="background"></div>
            <div class="hamburger">
              <div class="border"></div>
              <div class="border"></div>
              <div class="border"></div>
            </div>
            <div class="innerContent">
              <div class="menu-primary-menu-container">
                <?php wp_nav_menu( array(
                  'menu' => 'primary-menu',
                  'container' => false,
                  'menu_class' => 'link',
                ) ); ?>
              </div>
            </div>
          </div>
        </div>
      </div>
    </header>
    <div class="bodyBackground" style="background-image: url(<?php echo esc_url(wp_get_attachment_url(get_theme_mod('customTheme-main-callout-background'))); ?>);"></div>
    <div id="pageContainer" class="transition-fade">