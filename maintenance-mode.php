<?php
/**
 * Template for the maintenance mode page
 */

// Get maintenance settings
$maintenance = rewindrecords_get_maintenance_settings();

// Get location if enabled
$location = null;
if ($maintenance['show_location']) {
    $location = rewindrecords_get_location();
}

// Set the HTTP status code to 503 (Service Unavailable)
status_header(503);
header('Retry-After: 3600'); // Tell search engines to check back in an hour
?>
<!DOCTYPE html>
<html <?php language_attributes(); ?>>
<head>
    <meta charset="<?php bloginfo('charset'); ?>">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo esc_html($maintenance['title']); ?> - <?php bloginfo('name'); ?></title>
    <link rel="profile" href="https://gmpg.org/xfn/11">
    <?php if (has_site_icon()) : ?>
        <link rel="icon" href="<?php echo esc_url(get_site_icon_url(32)); ?>" sizes="32x32" />
        <link rel="icon" href="<?php echo esc_url(get_site_icon_url(192)); ?>" sizes="192x192" />
        <link rel="apple-touch-icon" href="<?php echo esc_url(get_site_icon_url(180)); ?>" />
        <meta name="msapplication-TileImage" content="<?php echo esc_url(get_site_icon_url(270)); ?>" />
    <?php endif; ?>
    <style>
        /* Google Fonts */
        @import url('https://fonts.googleapis.com/css2?family=Bebas+Neue&family=Lora:ital,wght@0,400;0,500;0,600;0,700;1,400;1,500;1,600;1,700&display=swap');
        /* Font Awesome for icons */
        @import url('https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css');

        /* Reset */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Lora', serif;
            background-color: <?php echo esc_attr($maintenance['bg_color']); ?>;
            color: #F5F5F5;
            line-height: 1.6;
            min-height: 100vh;
            text-align: center;
            padding: 2rem;
            position: relative;
        }

        html, body {
            height: 100%;
        }

        body:before {
            content: '';
            display: inline-block;
            height: 100%;
            vertical-align: middle;
            margin-right: -0.25em;
        }

        .maintenance-container {
            max-width: 800px;
            width: 100%;
            text-align: center;
            padding: 2rem;
            display: inline-block;
            vertical-align: middle;
        }

        .maintenance-logo {
            margin-bottom: 2rem;
            max-width: 300px;
            height: auto;
        }

        .maintenance-title {
            font-family: 'Bebas Neue', cursive;
            font-size: 4rem;
            font-weight: 400;
            letter-spacing: 2px;
            margin-bottom: 1.5rem;
            color: #FFFFFF;
        }

        .maintenance-message {
            font-size: 1.2rem;
            margin-bottom: 2rem;
            color: #CCCCCC;
        }

        .maintenance-location {
            display: block;
            margin-top: 2rem;
            font-size: 1.1rem;
            color: #E83A14;
            text-align: center;
        }

        .maintenance-location a {
            color: #E83A14;
            text-decoration: none;
            transition: color 0.3s ease;
        }

        .maintenance-location a:hover {
            text-decoration: underline;
        }

        .maintenance-location i {
            display: inline-block;
            vertical-align: middle;
            margin-right: 0.5rem;
        }

        @media (max-width: 768px) {
            .maintenance-title {
                font-size: 3rem;
            }

            .maintenance-message {
                font-size: 1rem;
            }

            .maintenance-location {
                font-size: 0.9rem;
            }
        }

        @media (max-width: 480px) {
            .maintenance-title {
                font-size: 2.5rem;
            }

            .maintenance-container {
                padding: 1rem;
            }
        }
    </style>
</head>
<body>
    <div class="maintenance-container">
        <?php if (!empty($maintenance['logo_url'])) : ?>
            <img src="<?php echo esc_url($maintenance['logo_url']); ?>" alt="<?php bloginfo('name'); ?>" class="maintenance-logo">
        <?php endif; ?>

        <h1 class="maintenance-title"><?php echo esc_html($maintenance['title']); ?></h1>

        <div class="maintenance-message">
            <?php echo wpautop($maintenance['message']); ?>
        </div>

        <?php if ($location && !empty($location['formatted_address'])) : ?>
            <div class="maintenance-location">
                <i class="fas fa-map-marker-alt"></i>
                <?php if (!empty($location['maps_link'])) : ?>
                    <a href="<?php echo esc_url($location['maps_link']); ?>" target="_blank" rel="noopener noreferrer">
                        <?php echo esc_html($location['formatted_address']); ?>
                    </a>
                <?php else : ?>
                    <span><?php echo esc_html($location['formatted_address']); ?></span>
                <?php endif; ?>
            </div>
        <?php endif; ?>
    </div>
</body>
</html>
<?php exit(); ?>
