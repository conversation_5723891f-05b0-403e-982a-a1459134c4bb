/* Record Single Page Styles */
.recordSingle {
    padding: 60px 0;
}

.recordSingle .recordHeader {
    display: block;
    margin-bottom: 40px;
    overflow: hidden;
}

.recordSingle .recordCover {
    display: inline-block;
    vertical-align: top;
    width: 300px;
    margin-right: 30px;
}

.recordSingle .recordCover img {
    width: 100%;
    height: auto;
    display: block;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    border-radius: 4px;
}

.recordSingle .recordInfo {
    display: inline-block;
    vertical-align: top;
    width: calc(100% - 330px);
}

.recordSingle .recordTitle {
    margin: 0 0 10px;
    font-size: 32px;
}

.recordSingle .recordArtist {
    margin: 0 0 20px;
    font-size: 24px;
    color: #666;
}

.recordSingle .recordMeta {
    margin-top: 20px;
    background: #f9f9f9;
    padding: 15px;
    border-radius: 4px;
}

.recordSingle .metaItem {
    margin-bottom: 10px;
}

.recordSingle .metaLabel {
    font-weight: bold;
    margin-right: 10px;
    min-width: 100px;
    display: inline-block;
}

.recordSingle .recordTracklist {
    margin-bottom: 40px;
}

.recordSingle .tracklistTable {
    width: 100%;
    border-collapse: collapse;
    font-size: 15px;
}

.recordSingle .tracklistTable th,
.recordSingle .tracklistTable td {
    padding: 10px;
    text-align: left;
    border-bottom: 1px solid #eee;
}

.recordSingle .tracklistTable th {
    font-weight: bold;
    border-bottom: 2px solid #ddd;
    text-transform: uppercase;
    font-size: 13px;
    letter-spacing: 0.5px;
}

.recordSingle .trackPosition {
    width: 60px;
    text-align: center;
}

.recordSingle th.trackPosition {
    text-align: center;
}

.recordSingle .trackDuration {
    width: 80px;
    text-align: right;
}

.recordSingle .disc-header td {
    padding-top: 20px;
    background-color: #f9f9f9;
    text-transform: uppercase;
    font-size: 13px;
    letter-spacing: 0.5px;
}

.recordSingle .recordDescription h2 {
    margin-bottom: 20px;
    font-size: 24px;
    border-bottom: 1px solid #eee;
    padding-bottom: 10px;
}

.recordSingle .recordContent {
    line-height: 1.6;
}

.recordSingle .recordContent p {
    margin-bottom: 15px;
}

/* Responsive styles */
@media (max-width: 768px) {
    .recordSingle .recordHeader {
        display: block;
    }

    .recordSingle .recordCover {
        display: block;
        width: 100%;
        max-width: 300px;
        margin: 0 auto 20px;
    }

    .recordSingle .recordInfo {
        display: block;
        width: 100%;
    }

    .recordSingle .tracklistTable {
        font-size: 14px;
    }

    .recordSingle .tracklistTable th {
        font-size: 12px;
    }

    .recordSingle .disc-header td {
        font-size: 12px;
    }
}

@media (max-width: 480px) {
    .recordSingle .tracklistTable {
        font-size: 13px;
    }

    .recordSingle .tracklistTable th,
    .recordSingle .tracklistTable td {
        padding: 8px 5px;
    }

    .recordSingle .trackPosition {
        width: 40px;
    }

    .recordSingle .trackDuration {
        width: 60px;
    }
}
